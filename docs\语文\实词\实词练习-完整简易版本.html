<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文言文实词 Flashcards</title>
    <style>
        body {
            font-family: 'KaiTi', 'STKaiti', serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
            background-color: #fdfaf4;
            padding: 20px;
            box-sizing: border-box;
            margin: 0;
            font-size: 18px;
            color: #333;
        }

        h1 {
            color: #5a4a42;
            margin-bottom: 25px;
            font-weight: normal;
            border-bottom: 2px solid #ccbbaa;
            padding-bottom: 10px;
            text-align: center; /* Center heading */
        }

        /* --- Selection Screen Styles --- */
        #selection-container {
            width: 95%;
            max-width: 500px; /* Smaller max-width for selection */
            background-color: #fff;
            border: 1px solid #ccbbaa;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            padding: 30px;
            box-sizing: border-box;
            text-align: center;
        }

        #selection-container h2 {
            font-weight: normal;
            color: #6a5a52;
            margin-top: 0;
            margin-bottom: 20px;
            font-size: 1.3em;
        }

        #selection-container .input-group {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px; /* Space between label and input */
        }

        #selection-container label {
            font-size: 1.1em;
            color: #444;
            width: 80px; /* Fixed width for alignment */
            text-align: right;
        }

        #selection-container input[type="text"] {
            padding: 10px;
            border: 1px solid #ccbbaa;
            border-radius: 4px;
            font-size: 1em;
            font-family: 'KaiTi', 'STKaiti', serif;
            width: 100px; /* Fixed width for number inputs */
            text-align: center;
        }

        #selection-container p.instructions {
            font-size: 0.9em;
            color: #777;
            margin-top: 0;
            margin-bottom: 20px;
        }

        #selection-error {
            color: #c00; /* Red */
            font-weight: bold;
            min-height: 1.2em; /* Reserve space */
            margin-bottom: 15px;
            font-size: 0.95em;
        }

        #start-button {
            padding: 12px 30px;
            background-color: #8a7a6f;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1.1em;
            transition: background-color 0.2s ease;
            font-family: 'KaiTi', 'STKaiti', serif;
            margin-top: 10px;
        }

        #start-button:hover {
            background-color: #6a5a52;
        }


        /* --- Flashcard Screen Styles (mostly unchanged) --- */
        #flashcard-container {
            margin-bottom: 20px;
            width: 95%;
            max-width: 700px;
        }

        #card {
            width: 100%;
            min-height: 200px;
            position: relative;
            background-color: #fff;
            border: 1px solid #ccbbaa;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            padding: 30px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
        }

        #card-front {
            width: 100%;
            box-sizing: border-box;
        }

        #question-area {
            font-size: 1.4em;
            margin-bottom: 25px;
            line-height: 1.8;
            color: #444;
            text-align: center;
        }
        #question-area .numbering {
            font-weight: bold;
            color: #888;
            margin-right: 8px;
            font-size: 0.9em;
        }


        #input-area {
            margin-top: 10px;
            margin-bottom: 25px;
        }

        #input-area .answer-input {
            display: block;
            width: calc(100% - 24px);
            padding: 12px;
            margin: 10px auto;
            border: 1px solid #ccbbaa;
            border-radius: 4px;
            font-size: 1.1em;
            font-family: 'KaiTi', 'STKaiti', serif;
        }
        #input-area .answer-input:disabled {
             background-color: #f5f5f5;
             cursor: not-allowed;
         }
        #input-area .answer-input::placeholder {
             color: #aaa;
             font-style: italic;
         }

        #input-area .instruction {
            font-size: 0.9em;
            color: #777;
            text-align: center;
            margin-top: 8px;
        }

         #result-display-area {
             margin-top: 25px;
             padding: 20px;
             background-color: #f7f3e9;
             border: 1px solid #e0d8c8;
             border-radius: 5px;
         }

         #feedback-message {
             font-weight: bold;
             margin-bottom: 15px;
             font-size: 1.2em;
             text-align: center;
         }
         #feedback-message.correct { color: #3a8e3a; }
         #feedback-message.incorrect { color: #c00; }


         #result-display-area .result-section {
             margin-bottom: 12px;
             padding-bottom: 12px;
             border-bottom: 1px dashed #d1c9bc;
         }
          #result-display-area .result-section:last-child {
              border-bottom: none;
              margin-bottom: 0;
              padding-bottom: 0;
          }


         #result-display-area .result-section strong {
             display: block;
             margin-bottom: 8px;
             color: #6a5a52;
             font-size: 1.05em;
         }

         #meaning-display, #translation-display, #source-display {
             font-size: 1.1em;
             color: #333;
             word-wrap: break-word;
             line-height: 1.7;
         }
         #meaning-display { font-weight: bold; color: #0056b3; }
         #source-display { font-style: italic; color: #555; font-size: 1em; }


         #next-question-button {
             display: block;
             margin: 25px auto 0;
             padding: 12px 30px;
             background-color: #8a7a6f;
             color: white;
             border: none;
             border-radius: 5px;
             cursor: pointer;
             font-size: 1.1em;
             transition: background-color 0.2s ease;
             font-family: 'KaiTi', 'STKaiti', serif;
         }

         #next-question-button:hover {
             background-color: #6a5a52;
         }


        #status-bar {
            margin: 25px 0;
            font-size: 1em;
            color: #555;
            width: 95%;
            max-width: 700px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        #status-back-to-home-button {
            background-color: #6c757d;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 12px;
            transition: background-color 0.2s ease;
            font-family: 'KaiTi', 'STKaiti', serif;
        }

        #status-back-to-home-button:hover {
            background-color: #545b62;
        }

        #export-button {
            padding: 10px 18px;
            background-color: #9a8a7f;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.2s ease;
            margin-top: 15px;
            font-family: 'KaiTi', 'STKaiti', serif;
        }

        #export-button:hover {
            background-color: #7a6a5f;
        }

        /* Visibility Control */
        .hidden {
            display: none;
        }


        /* Adjustments for smaller screens */
        @media (max-width: 600px) {
            body { padding: 15px; font-size: 16px;}
            h1 { font-size: 1.6em; }
            #selection-container { width: 100%; padding: 20px; }
            #selection-container .input-group { flex-direction: column; align-items: stretch; gap: 5px; }
            #selection-container label { width: auto; text-align: left; margin-bottom: 3px;}
            #selection-container input[type="text"] { width: 100%; box-sizing: border-box;}

            #flashcard-container, #status-bar { width: 100%; }
            #card { padding: 20px; }
            #question-area { font-size: 1.2em; }
            #question-area .numbering { font-size: 0.8em; }
            #input-area .answer-input { font-size: 1em; width: calc(100% - 20px); }
            #result-display-area { padding: 15px; }
            #meaning-display, #translation-display, #source-display { font-size: 1em; }
            #next-question-button, #export-button, #start-button { font-size: 1em; padding: 10px 20px; }
        }

        /* 艾宾浩斯学习计划表样式 */
        .ebbinghaus-container {
            font-family: 'KaiTi', 'STKaiti', serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
            background-color: #fdfaf4;
            padding: 20px;
            box-sizing: border-box;
            margin: 0;
            font-size: 16px;
            color: #333;
        }

        .ebbinghaus-container h1 {
            color: #5a4a42;
            margin-bottom: 25px;
            font-weight: normal;
            border-bottom: 2px solid #ccbbaa;
            padding-bottom: 10px;
            text-align: center;
        }

        #selection-container {
            width: 95%;
            max-width: 1300px;
            background-color: #fff;
            border: 1px solid #ccbbaa;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            padding: 30px;
            box-sizing: border-box;
            text-align: center;
            margin-bottom: 20px;
        }

        #selection-container h2 {
            font-weight: normal;
            color: #6a5a52;
            margin-top: 0;
            margin-bottom: 20px;
            font-size: 1.3em;
        }

        #selection-container h3 {
            font-weight: normal;
            color: #7a6a52;
            margin-top: 20px;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .instructions {
            margin-bottom: 20px;
            color: #666;
            line-height: 1.6;
        }

        #study-plan-setup-area {
            margin-top: 30px;
            border-top: 1px dashed #ccbbaa;
            padding-top: 20px;
        }

        #draggable-numbers-title {
            font-size: 1.1em;
            color: #444;
            margin-bottom: 10px;
            text-align: left;
        }

        #draggable-numbers-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            padding: 10px;
            border: 1px solid #e0d8c8;
            border-radius: 5px;
            background-color: #fdfaf4e0;
            margin-bottom: 20px;
            min-height: 50px;
            justify-content: flex-start;
        }

        .draggable-number {
            padding: 5px 10px;
            border: 1px solid #ccbbaa;
            border-radius: 4px;
            background-color: #fff;
            cursor: grab;
            font-family: 'KaiTi', 'STKaiti', serif;
            font-size: 0.9em;
            transition: background-color 0.2s, color 0.2s;
            user-select: none;
        }

        .draggable-number:active {
            cursor: grabbing;
        }

        .draggable-number.clickable {
            cursor: pointer;
            background-color: #e6f7ff;
            border-color: #4299e1;
        }

        .draggable-number.clickable:hover {
            background-color: #bae7ff;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(66, 153, 225, 0.3);
        }

        /* 表格样式 */
        #study-plan-table-container {
            overflow-x: auto;
        }

        #study-plan-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            font-size: 0.85em;
        }

        #study-plan-table th, #study-plan-table td {
            border: 1px solid #ccbbaa;
            padding: 6px;
            text-align: center;
            min-width: 55px;
            vertical-align: top;
        }

        #study-plan-table th {
            background-color: #f7f3e9;
            color: #6a5a52;
            font-weight: normal;
            white-space: nowrap;
        }

        /* 折叠列 */
        #study-plan-table th:nth-child(1),
        #study-plan-table td:nth-child(1) {
            min-width: 40px;
            width: 40px;
            max-width: 40px;
        }

        /* 序号列 - 能显示三位数字 */
        #study-plan-table th:nth-child(2),
        #study-plan-table td:nth-child(2) {
            min-width: 45px;
            width: 45px;
            max-width: 45px;
        }

        /* 学习日期列 - 能显示六位数字 */
        #study-plan-table th:nth-child(3),
        #study-plan-table td:nth-child(3) {
            min-width: 75px;
            width: 75px;
            max-width: 75px;
        }

        /* 学习内容列占用剩余空间 */
        #study-plan-table th:nth-child(4),
        #study-plan-table td:nth-child(4) {
            min-width: 200px;
            width: auto;
            max-width: none;
        }

        /* 复习列设置固定宽度 */
        #study-plan-table th:nth-child(n+5),
        #study-plan-table td:nth-child(n+5) {
            min-width: 70px;
            width: 70px;
            max-width: 70px;
        }

        /* 列宽优化 */
        .col-fold { width: 50px; }
        .col-seq { width: 60px; }
        .col-date { width: 180px; }
        .col-content { width: 250px; min-width: 250px; }
        .col-review { width: 90px; }

        .study-date-input {
            width: 90%;
            padding: 4px;
            font-size: 0.95em;
            border: 1px solid #ccc;
            border-radius: 3px;
            text-align: center;
            box-sizing: border-box;
            font-family: 'KaiTi', 'STKaiti', serif;
        }

        .study-content-cell {
            background-color: #fff;
            min-width: 120px;
            min-height: 40px;
            vertical-align: top;
            text-align: left;
            padding: 5px;
            display: flex;
            flex-wrap: wrap;
            align-items: flex-start;
            align-content: flex-start;
            gap: 3px;
            position: relative;
        }

        .study-content-cell.drag-over {
            background-color: #e6f7ff;
            border-style: dashed;
        }

        .dropped-item-tag {
            display: inline-flex;
            align-items: center;
            background-color: #d1e7fd;
            color: #084298;
            padding: 3px 6px;
            border-radius: 4px;
            margin: 2px;
            font-size: 0.95em;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .dropped-item-tag:hover {
            background-color: #4299e1;
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(66, 153, 225, 0.3);
        }

        .remove-item-btn {
            margin-left: 5px;
            color: #ae2a2a;
            cursor: pointer;
            font-weight: bold;
            padding: 0 3px;
            border-radius: 50%;
        }

        .remove-item-btn:hover {
            background-color: #f8d7da;
        }

        .study-date-input {
            width: 100%;
            border: 1px solid #dee2e6;
            background: white;
            text-align: center;
            font-size: 12px;
            padding: 4px 2px;
            border-radius: 4px;
            transition: border-color 0.3s ease;
            margin-bottom: 2px;
        }

        .review-cell-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
        }

        .review-cell-content input[type="text"] {
            width: 90%;
            padding: 3px;
            font-size: 0.85em;
            border: 1px solid #ccc;
            border-radius: 3px;
            text-align: center;
            box-sizing: border-box;
            font-family: 'KaiTi', 'STKaiti', serif;
        }

        .review-cell-content button {
            padding: 2px 6px;
            font-size: 0.8em;
            background-color: #8fbc8f;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-family: 'KaiTi', 'STKaiti', serif;
            margin-top: 2px;
        }

        .review-cell-content button:hover {
            background-color: #70a070;
        }

        .review-cell-content button:disabled {
            background-color: #ccc;
            color: #666;
            cursor: not-allowed;
        }

        .review-cell-content input[type="text"]:read-only {
            background-color: #f0f0f0;
            border-color: #ddd;
        }

        .study-content-cell {
            min-height: 40px;
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 6px;
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            padding: 6px;
            align-items: center;
            justify-content: flex-start;
            transition: all 0.3s ease;
        }

        .study-content-cell.drag-over {
            background: #e3f2fd;
            border-color: #667eea;
            border-style: solid;
        }

        .dropped-item-tag {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            cursor: pointer;
            white-space: nowrap;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
        }

        .dropped-item-tag:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .dropped-item-tag .remove-tag {
            margin-left: 6px;
            cursor: pointer;
            font-weight: bold;
            opacity: 0.8;
        }

        .dropped-item-tag .remove-tag:hover {
            opacity: 1;
        }

        /* 按钮样式 */
        .plan-row-buttons {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 15px;
        }

        #add-plan-row-button, #remove-plan-row-button {
            padding: 8px 15px;
            color: #333;
            border: 1px solid;
            border-radius: 4px;
            cursor: pointer;
            font-family: 'KaiTi', 'STKaiti', serif;
            transition: background-color 0.2s ease;
        }

        #add-plan-row-button {
            background-color: #90ee90;
            border-color: #7cb37c;
        }

        #add-plan-row-button:hover {
            background-color: #7ccd7c;
        }

        #remove-plan-row-button {
            background-color: #ffb3b3;
            border-color: #ff8080;
        }

        #remove-plan-row-button:hover {
            background-color: #ff9999;
        }

        #remove-plan-row-button:disabled {
            background-color: #e0e0e0;
            border-color: #ccc;
            color: #888;
            cursor: not-allowed;
        }

        .plan-action-buttons-container {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        #save-plan-button, #reset-plan-button, #print-button, #undo-button, #goto-practice-button {
            padding: 10px 18px;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.2s ease;
            font-family: 'KaiTi', 'STKaiti', serif;
        }

        #save-plan-button {
            background-color: #7e8a5f;
        }

        #reset-plan-button {
            background-color: #c86c5d;
        }

        #print-button {
            background-color: #28a745;
        }

        #undo-button {
            background-color: #6c757d;
        }

        .goto-practice-btn {
            background: linear-gradient(135deg, #4299e1, #3182ce);
        }

        #save-plan-button:hover {
            background-color: #6a734f;
        }

        #reset-plan-button:hover {
            background-color: #b05040;
        }

        #print-button:hover {
            background-color: #1e7e34;
        }

        #undo-button:hover {
            background-color: #545b62;
        }

        .goto-practice-btn:hover {
            background: linear-gradient(135deg, #3182ce, #2c5282);
        }

        #undo-button:disabled {
            background-color: #adb5bd;
            cursor: not-allowed;
        }

        /* 折叠功能样式 */
        .collapse-toggle {
            background-color: #f0f0f0;
            border: 1px solid #ccbbaa;
            cursor: pointer;
            padding: 4px 8px;
            font-size: 0.8em;
            color: #666;
            user-select: none;
            transition: background-color 0.2s;
            min-width: 30px;
            border-radius: 3px;
        }

        .collapse-toggle:hover {
            background-color: #e0e0e0;
        }

        .collapse-toggle.collapsed {
            background-color: #d0d0d0;
        }

        .row-group-collapsed {
            display: none;
        }

        .group-header-row {
            background-color: #f7f3e9;
            font-weight: bold;
        }

        .group-header-row td {
            background-color: #f7f3e9;
            border-bottom: 2px solid #ccbbaa;
        }

        #back-to-plan-button {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            margin-left: 10px;
        }

        #back-to-home-button {
            background-color: #6c757d;
            color: white;
            border: none;
            padding: 10px 18px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.2s ease;
            font-family: 'KaiTi', 'STKaiti', serif;
            margin-left: 10px;
        }

        #back-to-home-button:hover {
            background-color: #545b62;
        }
    </style>
</head>
<body>
    <!-- 艾宾浩斯学习计划表首页 -->
    <div id="ebbinghaus-homepage" class="ebbinghaus-container">
        <h1>文言文实词艾宾浩斯学习计划表</h1>

        <div id="selection-container">
            <h2>设置学习计划</h2>
            <p class="instructions">将下方的学习单元拖拽到计划表的"学习内容"栏中进行规划。<br>在各复习时间点手动输入复习的单元编号，并点击"完成"标记。<br><strong>💡 提示：每个格子可拖入多个学习单元，学习单元可重复使用，点击可直接进入对应的实词练习！每组包含约25个实词</strong></p>

            <div id="study-plan-setup-area">
                <div id="draggable-numbers-title">可拖拽的学习单元 (第1-30组，每组约25个实词) - 点击可直接练习:</div>
                <div id="draggable-numbers-container"></div>

                <div id="study-plan-table-container">
                    <h3>艾宾浩斯遗忘曲线复习计划表</h3>
                    <table id="study-plan-table">
                        <thead>
                            <tr>
                                <th style="width: 40px;">折叠</th>
                                <th>序号</th>
                                <th>学习日期</th>
                                <th>学习内容 (拖入单元)</th>
                                <th>第1天</th>
                                <th>第2天</th>
                                <th>第3天</th>
                                <th>第5天</th>
                                <th>第8天</th>
                                <th>第16天</th>
                                <th>第31天</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>

                <div class="plan-row-buttons">
                    <button id="add-plan-row-button">添加新学习日</button>
                    <button id="remove-plan-row-button">删减学习日</button>
                </div>

                <div class="plan-action-buttons-container">
                    <button id="print-button">🖨️ 打印学习表</button>
                    <button id="save-plan-button">💾 保存学习计划</button>
                    <button id="undo-button">↶ 撤销操作</button>
                    <button id="reset-plan-button">🗑️ 重置学习计划</button>
                    <button id="goto-practice-button" class="goto-practice-btn">📚 进入实词练习</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 练习页面 -->
    <div id="practice-page" style="display: none;">
        <h1>文言文实词练习</h1>

        <!-- Selection Screen -->
        <div id="practice-selection-container">
            <h2>选择练习范围</h2>
            <p class="instructions">请输入起始和结束编号 (例如 1.1, 15.99)。<br>留空则表示从头开始或到结尾。</p>
            <div class="input-group">
                <label for="start-number">起始编号:</label>
                <input type="text" id="start-number" placeholder="例如: 1.1">
            </div>
            <div class="input-group">
                <label for="end-number">结束编号:</label>
                <input type="text" id="end-number" placeholder="例如: 15.99">
            </div>
            <div id="selection-error"></div>
            <button id="start-button">开始练习</button>
            <button id="back-to-plan-button">返回学习计划</button>
        </div>
    </div>

    <!-- Flashcard Screen (Initially Hidden) -->
    <div id="flashcard-container" class="hidden">
        <div id="card">
            <div id="card-front">
                <div id="question-area">
                    <!-- Example sentence will load here -->
                </div>
                <div id="input-area">
                    <!-- Input field will load here -->
                </div>
                <!-- Result Area - Initially Hidden -->
                <div id="result-display-area" style="display: none;">
                    <div id="feedback-message"></div>
                    <div class="result-section">
                        <strong>正确词义:</strong>
                        <span id="meaning-display"></span>
                    </div>
                    <div class="result-section">
                        <strong>例句翻译:</strong>
                        <span id="translation-display"></span>
                    </div>
                     <div class="result-section">
                        <strong>出处:</strong>
                        <span id="source-display"></span>
                    </div>
                    <button id="next-question-button">下一题</button>
                    <button id="back-to-home-button">🏠 返回首页</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Bar (Initially Hidden) -->
    <div id="status-bar" class="hidden">
        <span>总数: <span id="total-questions">0</span></span>
        <span>剩余: <span id="remaining-questions">0</span></span>
        <button id="status-back-to-home-button">🏠 返回首页</button>
    </div>

    <!-- Export Button (Initially Hidden) -->
    <button id="export-button" class="hidden">导出默写记录 (TXT)</button>

    <!-- Embedded Data -->
    <script id="rawData" type="text/plain">
--- START OF FILE 实词表内容整理.txt ---

1.1 比: 靠近
例句: 其两膝相比者
翻译: 他们的两膝互相靠近
出处: 《核舟记》（魏学洢）

1.2 比: 及，等到......的时候
例句: 比至陈，车六七百乘
翻译: 等到到达陈地时，已有战车六七百辆
出处: 《陈涉世家》（司马迁）

1.3 比: 比较
例句: 心却比，男儿烈
翻译: 心志却比男儿还要刚烈
出处: 《满江红·小住京华》（秋瑾）

2.1 鄙: 浅陋无知，目光短浅
例句: 先帝不以臣卑鄙
翻译: 先帝不因为我身份低微、见识短浅
出处: 《出师表》（诸葛亮）

2.2 鄙: 浅陋无知，目光短浅
例句: 肉食者鄙，未能远谋
翻译: 当权者目光短浅，不能深谋远虑
出处: 《曹刿论战》（《左传》）

3.1 兵: 兵器，武器
例句: 兵革非不坚利也
翻译: 兵器并非不坚固锋利
出处: 《得道多助，失道寡助》（《孟子》）

3.2 兵: 军队
例句: 可汗大点兵
翻译: 可汗大规模征召军队
出处: 《木兰诗》（北朝民歌）

3.3 兵: 士兵
例句: 一老河兵闻之
翻译: 一位老河兵听说了这件事
出处: 《河中石兽》（纪昀）

3.4 兵: 战争
例句: 况乃未休兵
翻译: 更何况战争还未停止
出处: 《月夜忆舍弟》（杜甫）

4.1 病: 生病
例句: 未果，寻病终
翻译: 未能实现，不久后因病去世
出处: 《桃花源记》（陶渊明）

4.2 病: 枯萎
例句: 病树前头万木春
翻译: 枯萎的树木前头是万木争春
出处: 《酬乐天扬州初逢席上见赠》（刘禹锡）

5.1 乘: 坐、驾（车）
例句: 公与之乘
翻译: 鲁庄公和他同乘一辆战车
出处: 《曹刿论战》（《左传》）

5.2 乘: 趁着，冒着
例句: 从今若许闲乘月
翻译: 从今以后如果允许趁着月色闲游
出处: 《游山西村》（陆游）

5.3 乘: 辆（用以计算车子）
例句: 车六七百乘
翻译: 战车六七百辆
出处: 《陈涉世家》（司马迁）

6.1 持: 拉
例句: 锐兵刃，骰弓弩，持满
翻译: 磨快兵器，拉开弓弩，拉满弓弦
出处: 《周亚夫军细柳》（司马迁）

6.2 持: 拿着
例句: 屠乃奔倚其下，弛担持刀
翻译: 屠夫于是跑过去靠在柴堆下，放下担子拿起刀
出处: 《狼》（蒲松龄）

7.1 从: 跟从，跟随
例句: 战则请从
翻译: 如果作战，请允许我跟随
出处: 《曹刿论战》（《左传》）

7.2 从: 听从，顺从
例句: 小惠未遍，民弗从也
翻译: 小恩惠未能遍及百姓，百姓不会听从
出处: 《曹刿论战》（《左传》）

7.3 从: 采取某一种办法
例句: 无从致书以观
翻译: 没有办法得到书来阅读
出处: 《送东阳马生序》（宋濂）

7.4 从: 自，由
例句: 从小丘西行百二十步
翻译: 从小丘向西走一百二十步
出处: 《小石潭记》（柳宗元）

7.5 从: 与"容"相连：舒缓安闲，不慌不忙
例句: 鲦鱼出游从容
翻译: 鲦鱼悠闲自在地游动
出处: 《庄子·秋水》

8.1 达: 到达
例句: 指通豫南，达于汉阴
翻译: 一直通向豫州南部，到达汉水南岸
出处: 《愚公移山》（《列子》）

8.2 达: 得志，显贵
例句: 不求闻达于诸侯
翻译: 不追求在诸侯中显赫闻名
出处: 《出师表》（诸葛亮）

8.3 达: 有道德有学问（的人）
例句: 从乡之先达执经叩问
翻译: 拿着经书向当地有学问的前辈请教
出处: 《送东阳马生序》（宋濂）

8.4 达: 通顺，通畅
例句: 辞甚畅达
翻译: 文辞非常通顺流畅
出处: 《送东阳马生序》（宋濂）

9.1 当: 在（某时，某处）
例句: 陈康肃公尧咨善射，当世无双
翻译: 陈尧咨擅长射箭，当时无人能比
出处: 《卖油翁》（欧阳修）

9.2 当: 在（某时，某处）
例句: 咨臣以当世之事
翻译: 向我询问当时的天下大事
出处: 《出师表》（诸葛亮）

9.3 当: 应当，应该
例句: 但当涉猎，见往事耳
翻译: 只是应当粗略阅读，了解历史罢了
出处: 《孙权劝学》（《资治通鉴》）

9.4 当: 将要，就要
例句: 今当远离
翻译: 如今将要远离
出处: 《出师表》（诸葛亮）

9.5 当: 对着，面对
例句: 木兰当户织
翻译: 木兰对着门织布
出处: 《木兰诗》（北朝民歌）

9.6 当: 掌管，主持
例句: 卿今当涂掌事
翻译: 你现在掌权管事
出处: 《孙权劝学》（《资治通鉴》）

10.1 道: 路；途
例句: 伐竹取道，下见小潭，水尤清冽
翻译: 砍伐竹子开辟道路，下面发现一个小潭，水格外清澈
出处: 《小石潭记》（柳宗元）

10.2 道: 道理，规律，方法
例句: 虽有至道，弗学，不知其善也
翻译: 即使有最好的道理，不学习也不知道它的好处
出处: 《虽有嘉肴》（《礼记》）

10.3 道: 儒家推崇的上古时代的政治制度正道
例句: 大道之行也
翻译: 大道施行的时候
出处: 《大道之行也》（《礼记》）

10.4 道: 正道
例句: 得道者多助，失道者寡助
翻译: 施行仁政的人帮助多，不施仁政的人帮助少
出处: 《得道多助》（《孟子》）

10.5 道: 说，讲
例句: 此中人语云："不足为外人道也"
翻译: 这里的事不值得对外人说
出处: 《桃花源记》（陶渊明）

11.1 得: 得到，获得（取得）
例句: 一狼得骨止
翻译: 一只狼得到骨头就停下
出处: 《狼》（蒲松龄）

11.2 得: 看到，看见
例句: 林尽水源，便得一山
翻译: 桃林尽头看见一座山
出处: 《桃花源记》（陶渊明）

11.3 得: 领会
例句: 得之心而寓之酒也
翻译: 领会在心而寄托于酒
出处: 《醉翁亭记》（欧阳修）

11.4 得: 找到
例句: 既出，得其船
翻译: 出来后找到他的船
出处: 《桃花源记》（陶渊明）

11.5 得: 能够，可以
例句: 天子先驱至，不得入
翻译: 天子的先导到了，不能进入
出处: 《周亚夫军细柳》（司马迁）

11.6 得: 同"德"，感恩
例句: 所识穷乏者得我与
翻译: 贫穷的人会感激我吗
出处: 《鱼我所欲也》（《孟子》）

12.1 尔: 助词，相当于"而已""罢了"
例句: 无他，但手熟尔
翻译: 没有别的，只是手熟罢了
出处: 《卖油翁》（欧阳修）

12.2 尔: 助词，附在形容词、动词、副词或拟声词后，用作后缀
例句: 呼尔而与之，行道之人弗受
翻译: 吆喝着给人吃，过路的饥民也不会接受
出处: 《鱼我所欲也》（《孟子》）

12.3 尔: 词尾，相当于"地""然"
例句: 俶尔远逝
翻译: 忽然游向远处
出处: 《小石潭记》（柳宗元）

12.4 尔: 代词，你（你们）
例句: 尔安敢轻吾射
翻译: 你怎么敢轻视我射箭
出处: 《卖油翁》（欧阳修）

12.5 尔: 指示代词，这，那
例句: 尔来二十有一年矣
翻译: 从那时以来二十一年了
出处: 《出师表》（诸葛亮）

13.1 伐: 砍伐
例句: 伐竹取道，下见小潭
翻译: 砍伐竹子开辟道路，下面发现一个小潭
出处: 《小石潭记》（柳宗元）

13.2 伐: 攻打，讨伐
例句: 十年春，齐师伐我
翻译: 鲁庄公十年的春天，齐国军队攻打我国
出处: 《曹刿论战》（《左传》）

14.1 犯: 侵害，危害
例句: 至于亚夫，可得而犯邪
翻译: 至于周亚夫，能够侵犯吗
出处: 《周亚夫军细柳》（司马迁）

14.2 犯: 触犯
例句: 若有作奸犯科及为忠善者
翻译: 如果有做奸邪事情、触犯科条法令或尽忠行善的人
出处: 《出师表》（诸葛亮）

15.1 方: 方圆，周围
例句: 今齐地方千里，百二十城
翻译: 现在齐国的土地方圆千里，有一百二十座城池
出处: 《邹忌讽齐王纳谏》（《战国策》）

15.2 方: 区域，地方
例句: 有朋自远方来
翻译: 有朋友从远方来
出处: 《论语》十二章

15.3 方: 将，将要
例句: 方欲行，转视积薪后
翻译: 正想要走，转身看柴草堆后面
出处: 《狼》（蒲松龄）

15.4 方: 表示时间，相当于"始""才"
例句: 方鼓琴而志在太山
翻译: 刚开始弹琴时想着泰山
出处: 《伯牙鼓琴》

16.1 负: 背，以背载物
例句: 至于负者歌于途，行者休于树
翻译: 至于背着东西的人在途中唱歌，走路的人在树下休息
出处: 《醉翁亭记》（欧阳修）

16.2 负: 凭借，依仗，倚靠
例句: 皆生寒树，负势竞上
翻译: 都生长着耐寒的树木，依仗地势争着向上
出处: 《与朱元思书》（吴均）

17.1 赋: 一种文体
例句: 刻唐贤今人诗赋于其上
翻译: 把唐代和当代名人的诗赋刻在上面
出处: 《岳阳楼记》（范仲淹）

17.2 赋: 写作
例句: 为陈同甫赋壮词以寄之
翻译: 为陈同甫写一首雄壮的词寄给他
出处: 《破阵子·为陈同甫赋壮词以寄》（辛弃疾）

18.1 更: 另，另外
例句: 士别三日，即更刮目相待
翻译: 读书人分别三天，就应当用新的眼光看待
出处: 《孙权劝学》（《资治通鉴》）

18.2 更: 更加
例句: 室中更无人，惟有乳下孙
翻译: 家中再没有别人，只有一个还在吃奶的孙子
出处: 《石壕吏》（杜甫）

18.3 更: 又，还
例句: 湖中焉得更有此人
翻译: 湖中怎么还有这样的人
出处: 《湖心亭看雪》（张岱）

18.4 更: 夜间计时的单位
例句: 是日更定矣
翻译: 这天晚上八点左右
出处: 《湖心亭看雪》（张岱）

19.1 苟: 随便，马虎
例句: 苟全性命于乱世
翻译: 在乱世中苟且保全性命
出处: 《出师表》（诸葛亮）

19.2 苟: 勉强，不正当
例句: 故不为苟得也
翻译: 所以不做苟且取得的事
出处: 《鱼我所欲也》（《孟子》）

20.1 故: 过去，原来，照旧
例句: 温故而知新，可以为师矣
翻译: 温习旧知识而获得新理解，就可以当老师了
出处: 《论语》十二章

20.2 故: 原因，缘故
例句: 见两小儿辩斗，问其故
翻译: 看见两个小孩在争论，询问原因
出处: 《两小儿辩日》（《列子》）

20.3 故: 原因，缘故
例句: 既克，公问其故
翻译: 战胜后，鲁庄公询问原因
出处: 《曹刿论战》（《左传》）

20.4 故: 所以，因此
例句: 故君子有不战，战必胜矣
翻译: 所以君子不战则已，战则必胜
出处: 《得道多助》（《孟子》）

20.5 故: 特意
例句: 余故道为学之难以告之
翻译: 我特意讲述求学的艰难来告诉他
出处: 《送东阳马生序》（宋濂）

21.1 顾: 回头看，看，看见
例句: 元方入门不顾
翻译: 元方头也不回地进门
出处: 《陈太丘与友期行》（《世说新语》）

21.2 顾: 环顾，四顾
例句: 顾野有麦场
翻译: 看见田野里有个麦场
出处: 《狼》（蒲松龄）

21.3 顾: 拜访
例句: 三顾臣于草庐之中
翻译: 三次到草庐中拜访我
出处: 《出师表》（诸葛亮）

22.1 观: 看，仔细看
例句: 启窗而观
翻译: 打开窗户仔细看
出处: 《核舟记》（魏学洢）

22.2 观: 观赏
例句: 可远观而不可亵玩焉
翻译: 可以远远地观赏却不能靠近玩弄
出处: 《爱莲说》（周敦颐）

22.3 观: 观赏
例句: 予观夫巴陵胜状
翻译: 我观赏那巴陵郡的美景
出处: 《岳阳楼记》（范仲淹）

22.4 观: 景象
例句: 此则岳阳楼之大观也
翻译: 这就是岳阳楼的雄伟景象
出处: 《岳阳楼记》（范仲淹）

23.1 归: 归来，返回
例句: 朝而往，暮而归
翻译: 早晨前往，傍晚返回
出处: 《醉翁亭记》（欧阳修）

23.2 归: 归属，归依
例句: 微斯人，吾谁与归
翻译: 没有这样的人，我同谁一道呢
出处: 《岳阳楼记》（范仲淹）

23.3 归: 聚到一处
例句: 云归而岩穴暝
翻译: 云雾聚拢，山谷就显得昏暗了
出处: 《醉翁亭记》（欧阳修）

23.4 归: 女子出嫁
例句: 男有分，女有归
翻译: 男子有职业，女子有归宿
出处: 《大道之行也》（《礼记》）

24.1 过: 犯错
例句: 人恒过，然后能改
翻译: 人常常犯错，然后才能改正
出处: 《生于忧患，死于安乐》（《孟子》）

24.2 过: 过分，过于
例句: 以其境过清，不可久居
翻译: 因为这里的环境过于凄清，不能久留
出处: 《小石潭记》（柳宗元）

24.3 过: 过了，超过
例句: 过中不至，太丘舍去
翻译: 过了正午还没到，太丘就离开了
出处: 《陈太丘与友期行》（《世说新语》）

24.4 过: 经过
例句: 及鲁肃过寻阳，与蒙论议
翻译: 等到鲁肃经过寻阳，和吕蒙讨论
出处: 《孙权劝学》（《资治通鉴》）

25.1 好: 美丽的
例句: 好鸟相鸣，嘤嘤成韵
翻译: 美丽的鸟儿互相鸣叫，声音和谐动听
出处: 《与朱元思书》（吴均）

25.2 好: 使人满意，与"坏"相对
例句: 窈窕淑女，君子好逑
翻译: 文静美好的女子，是君子的好配偶
出处: 《关雎》（《诗经》）

25.3 好: 爱好
例句: 知之者不如好之者
翻译: 懂得它的人不如爱好它的人
出处: 《论语》十二章

26.1 号: 大叫，呼啸
例句: 阴风怒号，浊浪排空
翻译: 阴冷的风怒吼着，浑浊的浪冲向天空
出处: 《岳阳楼记》（范仲淹）

26.2 号: 取名号
例句: 故自号曰醉翁也
翻译: 所以给自己取别号叫"醉翁"
出处: 《醉翁亭记》（欧阳修）

27.1 还: 返回，回到
例句: 愿驰千里足，送儿还故乡
翻译: 希望骑上千里马，送我回到故乡
出处: 《木兰诗》（北朝民歌）

27.2 还: 交还，归还
例句: 计日以还
翻译: 计算着日子按时归还
出处: 《送东阳马生序》（宋濂）

28.1 会: 会合，聚会，聚集
例句: 迁客骚人，多会于此
翻译: 被贬的官员和诗人，大多在这里聚会
出处: 《岳阳楼记》（范仲淹）

28.2 会: 适逢，恰巧遇到
例句: 会宾客大宴
翻译: 正赶上大摆宴席招待宾客
出处: 《口技》（林嗣环）

28.3 会: 终将
例句: 会当凌绝顶
翻译: 终将登上泰山顶峰
出处: 《望岳》（杜甫）

29.1 惠: 恩惠，（给人以）好处
例句: 小惠未遍，民弗从也
翻译: 小恩小惠不能遍及百姓，百姓不会跟从
出处: 《曹刿论战》（《左传》）

29.2 惠: 同"慧"，聪明
例句: 甚矣，汝之不惠
翻译: 你太不聪明了
出处: 《愚公移山》（《列子》）

30.1 及: 到，到了，待，等到
例句: 及鲁肃过寻阳
翻译: 等到鲁肃经过寻阳
出处: 《孙权劝学》（《资治通鉴》）

30.2 及: 比得上
例句: 君美甚，徐公何能及君也
翻译: 您美极了，徐公怎么能比得上您呢
出处: 《邹忌讽齐王纳谏》（《战国策》）

30.3 及: 比得上
例句: 悲守穷庐，将复何及
翻译: 悲守穷困的陋室，后悔又怎么来得及
出处: 《诫子书》（诸葛亮）

30.4 及: 以及，和，与
例句: 至霸上及棘门军，直驰入
翻译: 到了霸上和棘门的军营，长驱直入
出处: 《周亚夫军细柳》（司马迁）

31.1 极: 尽头，极点，穷尽
例句: 其远而无所至极邪
翻译: 天空的远方是不是有尽头呢
出处: 《逍遥游》（《庄子》）

31.2 极: 到极点
例句: 感极而悲者矣
翻译: 感慨到极点而悲伤了
出处: 《岳阳楼记》（范仲淹）

31.3 极: （直达）
例句: 然则北通巫峡，南极潇湘
翻译: 既然这样，那么北面通向巫峡，南面直达潇湘
出处: 《岳阳楼记》（范仲淹）

31.4 极: 很，非常
例句: 初极狭，才通人
翻译: 起初非常狭窄，仅容一人通过
出处: 《桃花源记》（陶渊明）

32.1 计: 计算，计量，总计
例句: 通计一舟，为人五
翻译: 总计一条船上，刻了五个人
出处: 《核舟记》（魏学洢）

32.2 计: 计算，计量，总计
例句: 而计其长曾不盈寸
翻译: 可是计算它的长度竟不满一寸
出处: 《核舟记》（魏学洢）

33.1 济: 渡
例句: 欲济无舟楫
翻译: 想渡湖却没有船只
出处: 《望洞庭湖赠张丞相》（孟浩然）

34.1 假: 不真实的，不是本来的。与"真"相对
例句: 乃悟前狼假寐，盖以诱敌
翻译: 才明白前面的狼假装睡觉，原来是为了诱惑敌人
出处: 《狼》（蒲松龄）

34.2 假: 借
例句: 以是人多以书假余
翻译: 因此人们大多把书借给我
出处: 《送东阳马生序》（宋濂）

35.1 间: 中间
例句: 傅说举于版筑之间
翻译: 傅说从筑墙的劳作中被选拔
出处: 《生于忧患，死于安乐》（《孟子》）

35.2 间: 期间
例句: 奉命于危难之间
翻译: 在危难的时候接受使命
出处: 《出师表》（诸葛亮）

35.3 间: 夹杂，参与
例句: 肉食者谋之，又何间焉
翻译: 当权者自会谋划，你又何必参与呢
出处: 《曹刿论战》（《左传》）

35.4 间: 隔开，不连接  
例句: 遂与外人间隔  
翻译: 于是与外界的人隔绝开来  
出处: 《桃花源记》（陶渊明）  

36.1 简: 挑选，选拔  
例句: 盖简桃核修狭者为之  
翻译: 大概是挑选了修长而狭窄的桃核来雕刻  
出处: 《核舟记》（魏学洢）  

37.1 见: 看见  
例句: 见渔人，乃大惊  
翻译: 看见渔人，于是非常惊讶  
出处: 《桃花源记》（陶渊明）  

37.2 见: 了解  
例句: 但当涉猎，见往事耳  
翻译: 只是应当粗略阅读，了解历史罢了  
出处: 《孙权劝学》（《资治通鉴》）  

37.3 见: 领悟，知晓  
例句: 大兄何见事之晚乎  
翻译: 兄长怎么知晓事情这么晚呢  
出处: 《孙权劝学》（《资治通鉴》）  

37.4 见: 拜见，谒见  
例句: 归来见天子  
翻译: 回来拜见天子  
出处: 《木兰诗》（北朝民歌）  

37.5 见: 同"现"，表露  
例句: 食不饱，力不足，才美不外见  
翻译: 吃不饱，力气不足，才能和美德无法表露出来  
出处: 《马说》（韩愈）  

38.1 竭: 尽，完（耗尽，消失）  
例句: 一鼓作气，再而衰，三而竭  
翻译: 第一次击鼓士气高涨，第二次就衰弱了，第三次就耗尽了  
出处: 《曹刿论战》（《左传》）  

38.2 竭: 尽，完（耗尽，消失）  
例句: 彼竭我盈  
翻译: 他们的士气耗尽了，我们的士气正旺盛  
出处: 《曹刿论战》（《左传》）  

38.3 竭: 全部用上  
例句: 庶竭驽钝，攘除奸凶  
翻译: 希望能竭尽微薄之力，铲除奸邪凶恶之人  
出处: 《出师表》（诸葛亮）  

39.1 尽: 完，没有了  
例句: 林尽水源，便得一山  
翻译: 桃林的尽头是溪水的源头，便看到一座山  
出处: 《桃花源记》（陶渊明）  

39.2 尽: 完，没有了  
例句: 一屠晚归，担中肉尽  
翻译: 一个屠夫晚上回家，担子里的肉已经卖完了  
出处: 《狼》（蒲松龄）  

39.3 尽: 完，没有了  
例句: 骨已尽矣  
翻译: 骨头已经没有了  
出处: 《狼》（蒲松龄）  

39.4 尽: 吃完  
例句: 马之于里者，一食或尽粟一石  
翻译: 能跑千里的马，一顿饭有时能吃完一石粮食  
出处: 《马说》（韩愈）  

39.5 尽: 发挥完  
例句: 策之不以其道，食之不能尽其材  
翻译: 驾驭它不按照正确的方法，喂养它不能充分发挥它的才能  
出处: 《马说》（韩愈）  

39.6 尽: 全部，都（毫无保留）
例句: 至于斟酌损益，进尽忠言
翻译: 至于权衡利弊得失，毫无保留地进献忠言
出处: 《出师表》（诸葛亮）

40.1 进: 到朝廷，出来做官  
例句: 是进亦忧，退亦忧  
翻译: 这样出来做官也忧虑，不出来做官也忧虑  
出处: 《岳阳楼记》（范仲淹）  

40.2 进: 进献，奉献  
例句: 令初下，群臣进谏，门庭若市  
翻译: 命令刚下达时，群臣都来进献，门前像集市一样热闹  
出处: 《邹忌讽齐王纳谏》（《战国策》）  

40.3 进: 进献，奉献 
例句: 时时而间进  
翻译: 不时地有人进献  
出处: 《邹忌讽齐王纳谏》（《战国策》）  

41.1 居: 停留  
例句: 以其境过清，不可久居  
翻译: 因为这里的环境过于凄清，不能久留  
出处: 《小石潭记》（柳宗元）  

41.2 居: 居住，安居  
例句: 面山而居  
翻译: 面对着山居住  
出处: 《愚公移山》（《列子》）  

41.3 居: 处在，处于  
例句: 佛印居右，鲁直居左  
翻译: 佛印在右边，鲁直在左边  
出处: 《核舟记》（魏学洢）  

41.4 居: 经过，表示相隔一段时间  
例句: 居无何，上至，又不得入  
翻译: 过了不久，皇上到了，又不能进去  
出处: 《周亚夫军细柳》（司马迁）  

42.1 举: 选拔，推举  
例句: 胶鬲举于鱼盐之中  
翻译: 胶鬲是从鱼盐贩子中被选拔出来的  
出处: 《生于忧患，死于安乐》（《孟子》）  

43.1 具: 具备，具有  
例句: 罔不因势象形，各具情态  
翻译: 无不根据材料的形状雕刻形象，各有各的情态  
出处: 《核舟记》（魏学洢）  

43.2 具: 通"俱"，全部，详细地  
例句: 具答之  
翻译: 详细地回答他们  
出处: 《桃花源记》（陶渊明）  

43.3 具: 通"俱"，全部，详细地   
例句: 此人一一为具言所闻，皆叹惋  
翻译: 这个人详细地讲述了听到的事，大家都感叹惋惜  
出处: 《桃花源记》（陶渊明）  

44.1 俱: 一起  
例句: 虽与之俱学  
翻译: 虽然和他一起学习  
出处: 《学弈》（《孟子》）  

44.2 俱: 全，都  
例句: 青林翠竹，四时俱备  
翻译: 青翠的树林和竹子，四季都有  
出处: 《答谢中书书》（陶弘景）  

45.1 聚: 集合  
例句: 聚室而谋曰  
翻译: 召集全家人商量说  
出处: 《愚公移山》（《列子》）  

45.2 聚: 聚拢  
例句: 峰峦如聚  
翻译: 山峰像聚拢在一起  
出处: 《山坡羊·潼关怀古》（张养浩）  

46.1 遽: 就  
例句: 其父虽善游，其子岂遽善游哉  
翻译: 他的父亲虽然擅长游泳，他的儿子难道就擅长游泳吗  
出处: 《吕氏春秋·察今》  

47.1 决: 分辨，判断  
例句: 孔子不能决也  
翻译: 孔子也不能判断  
出处: 《两小儿辩日》（《列子》）  

47.2 决: 裂开  
例句: 决眦入归鸟  
翻译: 睁大眼睛看归鸟  
出处: 《望岳》（杜甫）  

48.1 绝: 断  
例句: 伯牙破琴绝弦  
翻译: 伯牙摔破琴，扯断弦  
出处: 《伯牙绝弦》  

48.2 绝: 与世隔绝的  
例句: 率妻子邑人来此绝境  
翻译: 带领妻子儿女和乡邻来到这个与世隔绝的地方  
出处: 《桃花源记》（陶渊明）  

48.3 绝: 独一无二的，独特的  
例句: 奇山异水，天下独绝  
翻译: 奇特的山，奇异的水，天下独一无二  
出处: 《与朱元思书》（吴均）  

48.4 绝: 极，非常  
例句: 佛印绝类弥勒  
翻译: 佛印非常像弥勒佛  
出处: 《核舟记》（魏学洢）  

48.5 绝: 停止，消失  
例句: 空谷传响，哀转久绝  
翻译: 空谷中传来回声，悲哀婉转，久久才消失  
出处: 《三峡》（郦道元）  

49.1 类: 类似，像  
例句: 佛印绝类弥勒  
翻译: 佛印非常像弥勒佛  
出处: 《核舟记》（魏学洢）  

50.1 临: 靠近  
例句: 有亭翼然临于泉上者，醉翁亭也  
翻译: 有一座亭子像鸟张开翅膀一样靠近泉水，这就是醉翁亭  
出处: 《醉翁亭记》（欧阳修）  

50.2 临: 来到  
例句: 临溪而渔  
翻译: 来到溪边捕鱼  
出处: 《醉翁亭记》（欧阳修）  

50.3 临: 面对，对着  
例句: 今当远离，临表涕零  
翻译: 如今将要远离，面对奏表流泪  
出处: 《出师表》（诸葛亮）  

50.4 临: 正当，将要，就要  
例句: 故临崩寄臣以大事也  
翻译: 所以临终前把国家大事托付给我  
出处: 《出师表》（诸葛亮）  

51.1 虑: 思虑，思绪，心思  
例句: 困于心，衡于虑  
翻译: 内心困扰，思虑阻塞  
出处: 《生于忧患，死于安乐》（《孟子》）  

52.1 论: 议论，谈论  
例句: 曹刿论战  
翻译: 曹刿谈论战争  
出处: 《曹刿论战》（《左传》）  

52.2 论: 评定，判定  
例句: 宜付有司论其行赏  
翻译: 应该交给有关部门评定他们的功劳给予奖赏  
出处: 《出师表》（诸葛亮）  

52.3 论: 言论  
例句: 众服为确论  
翻译: 大家都信服，认为是正确的言论  
出处: 《河中石兽》（纪昀）  

53.1 漫: 遍，到处都是  
例句: 惟解漫天作雪飞  
翻译: 只知道漫天飞舞像雪花一样  
出处: 《晚春》（韩愈）  

53.2 漫: 长  
例句: 故园东望路漫漫  
翻译: 向东遥望故乡，路途漫长  
出处: 《逢入京使》（岑参）  

54.1 明: 明亮，光明  
例句: 斗折蛇行，明灭可见  
翻译: 像北斗星一样曲折，像蛇一样蜿蜒，时隐时现  
出处: 《小石潭记》（柳宗元）  

54.2 明: 透明  
例句: 庭下如积水空明  
翻译: 庭院中的月光像积水一样清澈透明  
出处: 《记承天寺夜游》（苏轼）  

54.3 明: 普照，明媚  
例句: 至若春和景明  
翻译: 至于春风和煦，阳光明媚  
出处: 《岳阳楼记》（范仲淹）  

54.4 明: 英明  
例句: 以昭陛下平明之理  
翻译: 来显示陛下公正严明的治理  
出处: 《出师表》（诸葛亮）  

54.5 明: 次，下一个  
例句: 明日，徐公来，孰视之，自以为不如  
翻译: 第二天，徐公来了，仔细看他，自认为不如他  
出处: 《邹忌讽齐王纳谏》（《战国策》）  

54.6 明: 明确，坚定  
例句: 非淡泊无以明志  
翻译: 不淡泊名利就无法明确志向  
出处: 《诫子书》（诸葛亮）  

55.1 名: 名字，名称  
例句: 其船背稍夷，则题名其上  
翻译: 船的背面稍微平坦，就在上面题写名字  
出处: 《核舟记》（魏学洢）  

55.2 名: 命名，给...取名  
例句: 名之者谁  
翻译: 给它命名的人是谁  
出处: 《醉翁亭记》（欧阳修）  

55.3 名: 有名，出名  
例句: 山不在高，有仙则名  
翻译: 山不在于高，有仙人居住就出名  
出处: 《陋室铭》（刘禹锡）  

56.1 命: 命令，号令  
例句: 命夸娥氏二子负二山  
翻译: 命令夸娥氏的两个儿子背走两座山  
出处: 《愚公移山》（《列子》）  

56.2 命: 任命，委派  
例句: 受命以来，夙夜忧叹  
翻译: 接受任命以来，早晚忧虑叹息  
出处: 《出师表》（诸葛亮）  

56.3 命: 生命，性命  
例句: 苟全性命于乱世  
翻译: 在乱世中苟且保全性命  
出处: 《出师表》（诸葛亮）  

56.4 命: 文章体裁之一  
例句: 《兑命》曰"学学半"  
翻译: 《兑命》说"教别人，自己也能学一半"  
出处: 《虽有嘉肴》（《礼记》）  

57.1 难: 不容易，困难  
例句: 夫大国，难测也，惧有伏焉  
翻译: 大国难以预测，恐怕有埋伏  
出处: 《曹刿论战》（《左传》）  

57.2 难: 灾难  
例句: 奉命于危难之间  
翻译: 在危难的时候接受使命  
出处: 《出师表》（诸葛亮）  

58.1 平: 平坦  
例句: 土地平旷，屋舍俨然  
翻译: 土地平坦开阔，房屋整齐  
出处: 《桃花源记》（陶渊明）  

58.2 平: 填平，铲平  
例句: 吾与汝毕力平险  
翻译: 我和你们尽全力铲平险阻  
出处: 《愚公移山》（《列子》）  

58.3 平: 公平，公正  
例句: 以昭陛下平明之理  
翻译: 来显示陛下公正严明的治理  
出处: 《出师表》（诸葛亮）  

59.1 戚: 亲属（指族外）  
例句: 寡助之至，亲戚畔之  
翻译: 连亲属都会背叛他  
出处: 《得道多助，失道寡助》  

60.1 强: 强悍，强大  
例句: 知困，然后能自强也  
翻译: 知道困难，然后才能自强  
出处: 《虽有嘉肴》（《礼记》）  

60.2 强: 竭力，尽力（勉强，硬要）  
例句: 余强饮三大白而别  
翻译: 我勉强喝了三大杯酒告别  
出处: 《湖心亭看雪》（张岱）  

60.3 强: 有余  
例句: 赏赐百千强  
翻译: 赏赐很多财物  
出处: 《木兰诗》（北朝民歌）  

61.1 窃: 偷取  
例句: 盗窃乱贼而不作  
翻译: 偷盗和作乱的事不会发生  
出处: 《大道之行也》（《礼记》）  

62.1 请: 请求  
例句: 曹刿请见  
翻译: 曹刿请求拜见  
出处: 《曹刿论战》（《左传》）  

62.2 请: 请允许我（做某事）  
例句: 请以军礼见  
翻译: 请允许我用军礼相见  
出处: 《周亚夫军细柳》（司马迁）  

62.3 请: 请你（做某事）  
例句: 请循其本  
翻译: 请你回到问题的根本  
出处: 《庄子与惠子游于濠梁》（《庄子》）  

63.1 穷: 生活困难  
例句: 所识穷乏者得我与  
翻译: 我所认识的贫穷的人会感激我吗  
出处: 《鱼我所欲也》（《孟子》）  

63.2 穷: 穷尽，完结  
例句: 子子孙孙无穷匮也  
翻译: 子子孙孙没有穷尽  
出处: 《愚公移山》（《列子》）  

63.3 穷: 走到尽头，走完  
例句: 复前行，欲穷其林  
翻译: 继续前行，想走完这片林子  
出处: 《桃花源记》（陶渊明）  

63.4 穷: 极  
例句: 穷冬烈风  
翻译: 深冬时节，寒风凛冽  
出处: 《送东阳马生序》（宋濂）  

64.1 求: 探求，探索，追求，谋求，奢求  
例句: 予尝求古仁人之心  
翻译: 我曾经探求古代仁人的思想  
出处: 《岳阳楼记》（范仲淹）  

64.2 求: 寻求，寻找  
例句: 求二石兽于水中  
翻译: 在水中寻找两只石兽  
出处: 《河中石兽》（纪昀）  

64.3 求: 索取，要求，需求  
例句: 安求其能千里也  
翻译: 怎么能要求它日行千里呢  
出处: 《马说》（韩愈）  

65.1 取: 拿  
例句: 乃取一葫芦置于地  
翻译: 于是拿了一个葫芦放在地上  
出处: 《卖油翁》（欧阳修）  

65.2 取: 获得，讨取（开辟）（招致）  
例句: 伐竹取道，下见小谭，水尤清冽  
翻译: 砍伐竹子开辟道路，下面发现一个小潭，水格外清澈  
出处: 《小石潭记》（柳宗元）  

65.3 取: 选取，采用  
例句: 舍鱼而取熊掌者也  
翻译: 舍弃鱼而选择熊掌  
出处: 《鱼我所欲也》（《孟子》）  

66.1 去: 离开  
例句: 睨之，久而不去  
翻译: 斜着眼看他，很久都不离开  
出处: 《卖油翁》（欧阳修）  

66.2 去: 消逝，丧失  
例句: 意与日去，遂成枯落  
翻译: 意志随着时间消逝，最终变得枯槁  
出处: 《诫子书》（诸葛亮）  

66.3 去: 距，距离  
例句: 我以日始出时去人近  
翻译: 我认为太阳刚出来时离人近  
出处: 《两小儿辩日》（《列子》）  

67.1 全: 完整，整个  
例句: 全石以为底  
翻译: 用整块石头作为底部  
出处: 《小石潭记》（柳宗元）  

67.2 全: 完全，完备  
例句: 子之不知鱼之乐，全矣  
翻译: 你不知道鱼的快乐，是完全的  
出处: 《庄子与惠子游于濠梁》（《庄子》）  

67.3 全: 保全  
例句: 苟全性命于乱世  
翻译: 在乱世中苟且保全性命  
出处: 《出师表》（诸葛亮）  

68.1 任: 责任，职责  
例句: 故天将降大任于是人也  
翻译: 所以上天将要降下重大责任给这个人  
出处: 《生于忧患，死于安乐》（《孟子》）  

68.2 任: 委任，任用  
例句: 受任于败军之际  
翻译: 在军队失败的时候接受任命  
出处: 《出师表》（诸葛亮）  

68.3 任: 听凭，任凭  
例句: 从流飘荡，任意东西  
翻译: 随着流水飘荡，任凭船儿东西漂流  
出处: 《与朱元思书》（吴均）  

69.1 入: 进入，与"出"相对  
例句: 入楚则盗  
翻译: 进入楚国就会偷盗  
出处: 《晏子使楚》  

69.2 入: 注入  
例句: 自钱孔入，而钱不湿  
翻译: 从钱孔注入，而钱不湿  
出处: 《卖油翁》（欧阳修）  

69.3 入: 插入  
例句: 高峰入云，清流见底  
翻译: 高峰插入云霄，清澈的溪流见底  
出处: 《答谢中书书》（陶弘景）  

69.4 入: 进入朝廷，在国内  
例句: 入则无法家拂士  
翻译: 在国内如果没有守法度的大臣和辅佐的贤士  
出处: 《生于忧患，死于安乐》（《孟子》）  

70.1 若: 像，如，似  
例句: 细若蚊足  
翻译: 细得像蚊子的脚  
出处: 《核舟记》（魏学洢）  

70.2 若: 假如，如果  
例句: 若有作奸犯科及为忠善者  
翻译: 如果有做奸邪事情、触犯科条法令或尽忠行善的人  
出处: 《出师表》（诸葛亮）  

70.3 若: 你  
例句: 若屈伸呼吸，终日在天中行止  
翻译: 你的一举一动，呼吸之间，整天都在空气中活动  
出处: 《列子·天瑞》  

71.1 善: 好，好的，善良的  
例句: 善哉乎鼓琴  
翻译: 弹琴弹得真好啊  
出处: 《伯牙鼓琴》  

71.2 善: 善于，擅长  
例句: 通国之善弈者也  
翻译: 是全国最擅长下棋的人  
出处: 《学弈》（《孟子》）  

72.1 少: 不多，数量小  
例句: 饮少辄醉，而年又最高  
翻译: 喝一点就醉，而且年纪又最大  
出处: 《醉翁亭记》（欧阳修）  

72.2 少: 时间短  
例句: 少选之间而志在流水  
翻译: 一会儿又想着流水  
出处: 《伯牙鼓琴》  

72.3 少: 缺少  
例句: 但少闲人如吾两人者耳  
翻译: 只是缺少像我们两个这样的闲人罢了  
出处: 《记承天寺夜游》（苏轼）  

72.4 少: 年轻，少年，年纪小  
例句: 陈涉少时，尝与人佣耕  
翻译: 陈涉年轻时，曾经和别人一起被雇佣耕地  
出处: 《陈涉世家》（司马迁）  

73.1 舍: 房屋，房舍  
例句: 土地平旷，屋舍俨然  
翻译: 土地平坦开阔，房屋整齐  
出处: 《桃花源记》（陶渊明）  

73.2 舍: 舍弃，丢弃  
例句: 便舍船，从口入  
翻译: 于是舍弃船，从洞口进去  
出处: 《桃花源记》（陶渊明）  

73.3 舍: 同"释"，接触，消除  
例句: 其人舍然大喜  
翻译: 那个人消除疑虑，非常高兴  
出处: 《杞人忧天》（《列子》）  

74.1 涉: 浏览，阅读  
例句: 但当涉猎，见往事耳  
翻译: 只是应当粗略阅读，了解历史罢了  
出处: 《孙权劝学》（《资治通鉴》）  

75.1 生: 草木生长，出生，诞生  
例句: 绝巘多生怪柏  
翻译: 悬崖上生长着许多奇形怪状的柏树  
出处: 《三峡》（郦道元）  

75.2 生: 生存，活着  
例句: 然后知生于忧患而死于安乐也  
翻译: 这样才知道忧患使人生存，安逸享乐使人灭亡  
出处: 《生于忧患，死于安乐》（《孟子》）  

75.3 生: 生命  
例句: 生，亦我所欲也  
翻译: 生命也是我想要的  
出处: 《鱼我所欲也》（《孟子》）  

75.4 生: 后生，晚辈  
例句: 隶而从者，崔氏二小生  
翻译: 跟着去的还有崔家的两个年轻人  
出处: 《小石潭记》（柳宗元）  

76.1 胜: 胜利  
例句: 此所谓战胜于朝廷  
翻译: 这就是所谓的在朝廷上战胜敌人  
出处: 《邹忌讽齐王纳谏》（《战国策》）  

76.2 胜: 尽，完
例句: 臣不胜受恩感激
翻译: 臣实在承受不完（陛下）的恩情，感激不尽
出处: 《出师表》（诸葛亮）

76.3 胜: 优美的
例句: 予观夫巴陵胜状，在洞庭一湖
翻译: 我看那巴陵郡的美景，全集中在洞庭湖上
出处: 《岳阳楼记》（范仲淹）

77.1 师: 军队  
例句: 十年春，齐师伐我  
翻译: 鲁庄公十年的春天，齐国军队攻打我国  
出处: 《曹刿论战》（《左传》）  

77.2 师: 老师  
例句: 温故而知新，可以为师矣  
翻译: 温习旧知识而获得新理解，就可以当老师了  
出处: 《论语》十二章  

77.3 师: 首都，京城  
例句: 余朝京师，生以乡人子谒余  
翻译: 我到京城朝见，同乡的年轻人来拜见我  
出处: 《送东阳马生序》（宋濂）  

78.1 施: 实施，实行  
例句: 悉以咨之，然后施行  
翻译: 都拿来询问他们，然后实施  
出处: 《出师表》（诸葛亮）  

79.1 实: 实际，事实（实在）  
例句: 实是欲界之仙都  
翻译: 实在是人间仙境  
出处: 《答谢中书书》（陶弘景）  

79.2 实: 诚实，真实  
例句: 此皆良实  
翻译: 这些都是善良诚实的人  
出处: 《出师表》（诸葛亮）  

80.1 食: 吃，吃饭  
例句: 弗食，不知其旨也  
翻译: 不吃，不知道它的美味  
出处: 《虽有嘉肴》（《礼记》）  

80.2 食: 粮食，食物  
例句: 便要还家，设酒杀鸡作食  
翻译: 就邀请他回家，摆酒杀鸡做饭  
出处: 《桃花源记》（陶渊明）  

80.3 食: 供养，给...吃  
例句: 食之不能尽其材  
翻译: 喂养它不能充分发挥它的才能  
出处: 《马说》（韩愈）  

81.1 使: 使唤，派遣  
例句: 于是上乃使使持节诏将军  
翻译: 于是皇上就派使者拿着符节去诏令将军  
出处: 《周亚夫军细柳》（司马迁）  

81.2 使: 让，叫，令；使得，致使  
例句: 使弈秋诲二人弈  
翻译: 让弈秋教两个人下棋  
出处: 《学弈》（《孟子》）  

81.3 使: 出使  
例句: 安陵君因使唐雎使于秦  
翻译: 安陵君于是派唐雎出使秦国  
出处: 《唐雎不辱使命》（《战国策》）  

81.4 使: 使节  
例句: 于是上乃使使持节诏将军  
翻译: 于是皇上就派使者拿着符节去诏令将军  
出处: 《周亚夫军细柳》（司马迁）  

81.5 使: 假使，纵使，即使  
例句: 只使坠，亦不能有所中伤  
翻译: 即使掉下来，也不会有什么伤害  
出处: 《杞人忧天》（《列子》）  

82.1 释: 放下  
例句: 有卖油翁释担而立  
翻译: 有个卖油的老翁放下担子站着  
出处: 《卖油翁》（欧阳修）  

83.1 市: 市场，集市  
例句: 百里奚举于市  
翻译: 百里奚从市场上被选拔出来  
出处: 《生于忧患，死于安乐》（《孟子》）  

83.2 市: 买  
例句: 愿为市鞍马  
翻译: 愿意为此去买鞍马  
出处: 《木兰诗》（北朝民歌）  

84.1 恃: 依赖，依仗  
例句: 富者曰："子何恃而往？"  
翻译: 富人说："你依靠什么去呢？"  
出处: 《为学》（彭端淑）  

85.1 数: 几，几个  
例句: 夹岸数百步，中无杂树  
翻译: 两岸几百步内，没有别的树  
出处: 《桃花源记》（陶渊明）  

85.2 数: 计算，点数  
例句: 珠可历历数也  
翻译: 念珠可以清清楚楚地数出来  
出处: 《核舟记》（魏学洢）  

86.1 属: 同"嘱"，嘱托  
例句: 属予作文以记之  
翻译: 嘱托我写文章来记录这件事  
出处: 《岳阳楼记》（范仲淹）  

86.2 属: 部属，隶属，归属  
例句: 壁门士吏谓从属车骑曰  
翻译: 营门的军官对随从的车骑说  
出处: 《周亚夫军细柳》（司马迁）  

86.3 属: 类，一类  
例句: 有良田，美池，桑竹之属  
翻译: 有肥沃的田地，美丽的池塘，桑树竹子之类  
出处: 《桃花源记》（陶渊明）  

86.4 属: 像，类似  
例句: 神情与苏、黄不属  
翻译: 神情和苏、黄不相类似  
出处: 《核舟记》（魏学洢）  

86.5 属: 连接  
例句: 属引凄异，空谷传响  
翻译: 声音连续不断，凄凉怪异，在空谷中回荡  
出处: 《三峡》（郦道元）  

87.1 说: 陈述，解说，讲  
例句: 及郡下，诣太守，说如此  
翻译: 到了郡城，拜见太守，讲述了这些情况  
出处: 《桃花源记》（陶渊明）  

87.2 说: 古代文体  
例句: 爱莲说，马说  
翻译: 《爱莲说》，《马说》  
出处: 无  

87.3 说: 同"悦"，高兴  
例句: 学而时习之，不亦说乎  
翻译: 学习并时常复习，不也很高兴吗  
出处: 《论语》十二章  

88.1 素: 白色的  
例句: 春冬之时，则素湍绿潭  
翻译: 春冬季节，白色的急流映衬着绿色的深潭  
出处: 《三峡》（郦道元）  

88.2 素: 朴素，不加装饰的  
例句: 可以调素琴，阅金经  
翻译: 可以弹奏不加装饰的琴，阅读佛经  
出处: 《陋室铭》（刘禹锡）  

89.1 汤: 热水，开水  
例句: 日初出沧沧凉凉，及日中如探汤  
翻译: 太阳刚出来时清凉，到了中午就像把手伸进热水里一样热  
出处: 《两小儿辩日》（《列子》）  

89.2 汤: 水流大而急的样子  
例句: 汤汤乎若流水  
翻译: 浩浩荡荡像流水一样  
出处: 《伯牙鼓琴》  

90.1 徒: 空，光  
例句: 亦免冠徒跣，以头抢地尔  
翻译: 也不过是摘掉帽子，光着脚，用头撞地罢了  
出处: 《唐雎不辱使命》（《战国策》）  

91.1 亡: 灭亡，死亡  
例句: 出则无敌国外患者，国恒亡  
翻译: 在国外如果没有敌对国家和外患，国家往往会灭亡  
出处: 《生于忧患，死于安乐》（《孟子》）  

91.2 亡: 无，没有  
例句: 河曲智叟亡以应  
翻译: 河曲智叟无话可答  
出处: 《愚公移山》（《列子》）  

92.1 为: 做，干  
例句: 行拂乱其所为  
翻译: 做事违背他的意愿  
出处: 《生于忧患，死于安乐》（《孟子》）  

92.2 为: 雕刻，制作，制造  
例句: 盖简桃核修狭者为之  
翻译: 大概是挑选了修长而狭窄的桃核来雕刻  
出处: 《核舟记》（魏学洢）  

92.3 为: 作为  
例句: 武陵人捕鱼为业  
翻译: 武陵人以捕鱼为职业  
出处: 《桃花源记》（陶渊明）  

92.4 为: 担任，充当  
例句: 是以众议举宠为督  
翻译: 因此大家商议推举宠担任督军  
出处: 《出师表》（诸葛亮）  

92.5 为: 是
例句: 中俄冠而多髯者为东坡
翻译: （人群中）戴着高帽子且胡须浓密的人被称为苏东坡
出处: 《核舟记》（魏学洢）

92.6 为: 叫做，称为  
例句: 北冥有鱼，其名为鲲  
翻译: 北海有一条鱼，它的名字叫做鲲  
出处: 《逍遥游》（庄子）  

92.7 为: 变成，成为  
例句: 化而为鸟  
翻译: 变化成为鸟  
出处: 《逍遥游》（庄子）  

92.8 为: 形成  
例句: 为坻，为屿，为嵁，为岩  
翻译: 形成坻、屿、嵁、岩等形态  
出处: 《小石潭记》（柳宗元）  

92.9 为: 为了，给，替  
例句: 为人谋而不忠乎  
翻译: 为别人谋划却不忠诚吗  
出处: 《论语》  

92.10 为: 被  
例句: 天子为动，改容式车  
翻译: 天子被感动，改变神色，扶轼致敬  
出处: 《史记》  

92.11 为: 认为  
例句: 孰为汝多知乎  
翻译: 谁认为你知识渊博呢  
出处: 《列子》

93.1 委: 抛弃，舍弃  
例句: 与人期行，相委而去  
翻译: 和别人约好同行，却丢下别人自己走了  
出处: 《陈太丘与友期行》（《世说新语》）  

94.1 务: 事务，事情  
例句: 蒙辞以军中多务  
翻译: 吕蒙用军中事务繁多来推辞  
出处: 《孙权劝学》（《资治通鉴》）  

95.1 鲜: 鲜艳，鲜明  
例句: 芳草鲜美，落英缤纷  
翻译: 芳香的青草鲜艳美丽，飘落的花瓣繁多交杂  
出处: 《桃花源记》（陶渊明）  

95.2 鲜: 滋味鲜美  
例句: 无鲜肥滋味之享  
翻译: 没有新鲜肥美的东西可以享用  
出处: 《送东阳马生序》（宋濂）  

95.3 鲜: 少（xiǎn）  
例句: 菊之爱，陶后鲜有闻  
翻译: 对于菊花的喜爱，陶渊明之后就很少听说了  
出处: 《爱莲说》（周敦颐）  

96.1 向: 对着，朝着  
例句: 磨刀霍霍向猪羊  
翻译: 磨刀的声音霍霍地响对着猪羊  
出处: 《木兰诗》（北朝民歌）  

96.2 向: 过去，从前，原来  
例句: 便扶向路，处处志之  
翻译: 就沿着原来的路回去，处处都做标记  
出处: 《桃花源记》（陶渊明）  

97.1 效: 任务，重任  
例句: 愿陛下托臣以讨贼兴复之效  
翻译: 希望陛下把讨伐奸贼、复兴汉室的任务交给我  
出处: 《出师表》（诸葛亮）  

97.2 效: 效果，见效，奏效  
例句: 夙夜忧叹，恐托付不效  
翻译: 早晚忧愁叹息，担心托付给我的任务没有成效  
出处: 《出师表》（诸葛亮）  

98.1 谢: 道谢  
例句: 使人称谢  
翻译: 派人向将士们致谢  
出处: 《周亚夫军细柳》（司马迁）  

98.2 谢: 道歉  
例句: 秦王色挠，长跪而谢之曰  
翻译: 秦王面露胆怯之色，直身跪着向唐雎道歉说  
出处: 《唐雎不辱使命》（《战国策》）  

99.1 信: 信用，诚信  
例句: 日中不至，则是无信  
翻译: 正午不到，就是不讲信用  
出处: 《陈太丘与友期行》（《世说新语》）  

99.2 信: 相信，信任  
例句: 忌不自信，而复问其妾曰  
翻译: 邹忌不相信自己（比徐公美），又问他的妾说  
出处: 《邹忌讽齐王纳谏》（《战国策》）  

100.1 行: 行走  
例句: 陈太丘与友期行  
翻译: 陈太丘和朋友约好一同出行  
出处: 《陈太丘与友期行》（《世说新语》）  

100.2 行: 行动  
例句: 终日在天中行止，奈何忧崩坠乎  
翻译: 整天都在天空中活动，为什么要担心天会塌下来呢  
出处: 《杞人忧天》（《列子》）  

100.3 行: 爬行  
例句: 斗折蛇行  
翻译: （溪水）像北斗星那样曲折，像蛇那样蜿蜒爬行  
出处: 《小石潭记》（柳宗元）  

100.4 行: 通行，出行  
例句: 商旅不行  
翻译: 商人和旅客不能通行  
出处: 《岳阳楼记》（范仲淹）  

100.5 行: 划船，划行  
例句: 缘溪行  
翻译: 沿着溪水划船前行  
出处: 《桃花源记》（陶渊明）  

100.6 行: 做，实行，实施（做事）
例句: 行拂乱其所为  
翻译: 做事总是违背他的意愿  
出处: 《生于忧患，死于安乐》（《孟子》）  

100.7 行: 品德，品行  
例句: 夫君子之行，静以修身，俭以养德  
翻译: 君子的品行，靠静来修养身心，靠俭来培养品德  
出处: 《诫子书》（诸葛亮）  

100.8 行: （háng）行列，指队伍，军队  
例句: 必能使行阵和睦，优劣得所  
翻译: 一定能使军队团结协作，才能高的人和才能低的人都得到合理安置  
出处: 《出师表》（诸葛亮）  

101.1 形: 形体，躯体  
例句: 无案牍之劳形  
翻译: 没有官府的公文使身体劳累  
出处: 《陋室铭》（刘禹锡）  

101.2 形: 形状，样子  
例句: 罔不因势象形  
翻译: 无不就着（材料原来的）形状雕刻成（各种事物的）形象  
出处: 《核舟记》（魏学洢）  

101.3 形: 形迹，踪迹，踪影  
例句: 日星隐曜，山岳潜形  
翻译: 太阳和星星隐藏了光辉，山岳隐没了形迹  
出处: 《岳阳楼记》（范仲淹）  

102.1 兴: 起，兴起  
例句: 清风徐来，水波不兴  
翻译: 清风缓缓吹来，水面波澜不起  
出处: 《赤壁赋》（苏轼）  

102.2 兴: 兴办，创办  
例句: 越明年，政通人和，百废具兴  
翻译: 到了第二年，政事顺利，百姓和乐，各种荒废的事业都兴办起来了  
出处: 《岳阳楼记》（范仲淹）  

102.3 兴: 兴盛，振兴，复兴  
例句: 此先帝所以兴隆也  
翻译: 这是先帝（汉室）能够兴盛的原因  
出处: 《出师表》（诸葛亮）  

103.1 修: 修建，建造  
例句: 乃重修岳阳楼  
翻译: 于是重新修建岳阳楼  
出处: 《岳阳楼记》（范仲淹）  

103.2 修: 长，高  
例句: 盖简桃核修狭者为之  
翻译: 大概是挑选了又长又窄的桃核来雕刻  
出处: 《核舟记》（魏学洢）  

103.3 修: 培养  
例句: 静以修身，俭以养德  
翻译: 以宁静来修养身心，以节俭来培养品德  
出处: 《诫子书》（诸葛亮）  

104.1 徐: 缓慢，缓缓地，慢慢地  
例句: 徐以杓酌油沥之  
翻译: 慢慢地用勺子舀油滴入（葫芦）  
出处: 《卖油翁》（欧阳修）  

105.1 许: 表约数，上下，光景  
例句: 高可二黍许  
翻译: 高度大约有两粒黄米那么高  
出处: 《核舟记》（魏学洢）  

105.2 许: 表约数，上下，光景  
例句: 潭中鱼可百许头  
翻译: 潭中的鱼大约有一百来条  
出处: 《小石潭记》（柳宗元）  

105.3 许: 赞同，答应，应允  
例句: 杂然相许  
翻译: 纷纷表示赞同  
出处: 《愚公移山》（列子）  

106.1 寻: 寻找  
例句: 寻向所志，遂迷，不复得路  
翻译: 寻找之前做的标记，却迷失了方向，再也找不到路  
出处: 《桃花源记》（陶渊明）  

106.2 寻: 不久，随即  
例句: 未果，寻病终  
翻译: 没有实现，不久就病死了  
出处: 《桃花源记》（陶渊明）  

107.1 业: 事业，功业  
例句: 先帝创业未半而中道崩殂  
翻译: 先帝开创的事业还没有完成一半，就中途去世了  
出处: 《出师表》（诸葛亮）  

107.2 业: 职业  
例句: 晋太元中，武陵人捕鱼为业  
翻译: 晋朝太元年间，武陵人以捕鱼为职业  
出处: 《桃花源记》（陶渊明）  

107.3 业: 学业，学习的功课  
例句: 其业有不精、德有不成者  
翻译: 如果他们学业不精通、品德没有养成  
出处: 《送东阳马生序》（宋濂）  

108.1 遗: 遗留，留下  
例句: 邻人京城氏之孀妻有遗男  
翻译: 邻居京城氏的寡妇有个遗孤  
出处: 《愚公移山》（列子）  

108.2 遗: wèi 送给，馈赠  
例句: 是以先帝简拔以遗陛下  
翻译: 因此先帝选拔他们来留给陛下  
出处: 《出师表》（诸葛亮）  

109.1 贻: 赠给  
例句: 尝贻余核舟一  
翻译: 曾经赠送给我一只核舟  
出处: 《核舟记》（魏学洢）  

110.1 夷: 平坦  
例句: 其船背稍夷  
翻译: 船的背面比较平坦  
出处: 《核舟记》（魏学洢）  

110.2 夷: 平易  
例句: 言和而色夷  
翻译: 言语温和，神色平易  
出处: 《送东阳马生序》（宋濂）  

111.1 异: 不同，不同的  
例句: 或异二者之为，何哉  
翻译: 或许不同于以上两种表现，为什么呢  
出处: 《岳阳楼记》（范仲淹）  

111.2 异: 特别的，奇特的，奇异的  
例句: 奇山异水，天下独绝  
翻译: 奇特的山，奇异的水，天下独一无二  
出处: 《与朱元思书》（吴均）  

111.3 异: 惊异，惊奇，奇怪  
例句: 渔人甚异之  
翻译: 渔人对此感到非常惊奇  
出处: 《桃花源记》（陶渊明）  

112.1 易: 更换  
例句: 寒暑易节，始一反焉  
翻译: 冬夏换季，才往返一次  
出处: 《愚公移山》（列子）  

112.2 易: 交换  
例句: 寡人欲以五百里之地易安陵  
翻译: 我想用五百里的土地交换安陵  
出处: 《唐雎不辱使命》（战国策）  

113.1 诣: 到，到达，到……去（拜访，遇见）  
例句: 及郡下，诣太守说如此  
翻译: 到了郡城，去拜见太守，报告了这些情况  
出处: 《桃花源记》（陶渊明）  

114.1 益: 增加，增长  
例句: 所以动心忍性，曾益其所不能  
翻译: 用来使他的内心受到震动，性格变得坚韧，增加他所不具备的能力  
出处: 《生于忧患，死于安乐》（孟子）  

114.2 益: 好处  
例句: 孤常读书，自以为大有所益  
翻译: 我经常读书，自认为大有好处  
出处: 《孙权劝学》（资治通鉴）  

114.3 益: 更，更加  
例句: 香远益清  
翻译: 香气远播，更加清幽  
出处: 《爱莲说》（周敦颐）  

115.1 阴: 山之北，水之南  
例句: 达于汉阴，可乎  
翻译: 到达汉水的南岸，可以吗  
出处: 《愚公移山》（列子）  

115.2 阴: 阴沉，阴冷  
例句: 阴风怒号  
翻译: 阴冷的风怒吼着  
出处: 《岳阳楼记》（范仲淹）  

115.3 阴: 树阴，阴影  
例句: 野芳发而幽香，佳木秀而繁阴  
翻译: 野花开放，散发出幽香，树木枝繁叶茂，形成浓密的树阴  
出处: 《醉翁亭记》（欧阳修）  

116.1 引: 称引，譬喻  
例句: 不宜妄自菲薄，引喻失义  
翻译: 不应该随便看轻自己，说话不恰当  
出处: 《出师表》（诸葛亮）  

116.2 引: 拉，牵引  
例句: 友人惭，下车引之  
翻译: 朋友感到惭愧，下车拉他  
出处: 《陈太丘与友期行》（世说新语）  

116.3 引: 延长  
例句: 属引凄异  
翻译: 声音接连不断，凄凉怪异  
出处: 《三峡》（郦道元）  

117.1 盈: 满，充满  
例句: 而计其长曾不盈寸  
翻译: 但计算它的长度还不满一寸  
出处: 《核舟记》（魏学洢）  

117.2 盈: 增长，旺盛  
例句: 彼竭我盈，故克之  
翻译: 对方士气衰竭，我方士气旺盛，所以战胜了他们  
出处: 《曹刿论战》（左传）  

118.1 余: 我，我的  
例句: 尝贻余核舟一  
翻译: 曾经赠送给我一只核舟  
出处: 《核舟记》（魏学洢）  

118.2 余: 剩下，多余  
例句: 以残年余力  
翻译: 凭残余的岁月和剩下的力气  
出处: 《愚公移山》（列子）  

118.3 余: 多，表示整数后不定的零数  
例句: 阅十余岁  
翻译: 过了十多年  
出处: 《河中石兽》（纪昀）  

118.4 余: 以外，以后，其他，其余  
例句: 余人各复延至其家，皆出酒食  
翻译: 其余的人各自又邀请渔人到他们家，都拿出酒饭（招待他）  
出处: 《桃花源记》（陶渊明）  

119.1 狱: 诉讼案件  
例句: 小大之狱  
翻译: 大大小小的诉讼案件  
出处: 《曹刿论战》（左传）  

120.1 御: 驾驭  
例句: 虽乘奔御风，不以疾也  
翻译: 即使骑着飞奔的马，驾着风，也没有这么快  
出处: 《三峡》（郦道元）  

121.1 缘: 沿着，顺着  
例句: 缘溪行，忘路之远近  
翻译: 沿着溪水划船，忘记了路程的远近  
出处: 《桃花源记》（陶渊明）  

122.1 远: 遥远（指空间距离大）  
例句: 忘路之远近  
翻译: 忘记了路程的远近  
出处: 《桃花源记》（陶渊明）  

122.2 远: 高远  
例句: 非宁静无以致远  
翻译: 不宁静就无法达到远大的目标  
出处: 《诫子书》（诸葛亮）  

122.3 远: 偏僻之地  
例句: 处江湖之远则忧其君  
翻译: 处在偏远的江湖就为君主担忧  
出处: 《岳阳楼记》（范仲淹）  

122.4 远: 深远  
例句: 肉食者鄙，未能远谋  
翻译: 当权者目光短浅，不能深谋远虑  
出处: 《曹刿论战》（左传）  

122.5 远: 疏远  
例句: 亲贤臣，远小人  
翻译: 亲近贤臣，疏远小人  
出处: 《出师表》（诸葛亮）  

123.1 云: 说  
例句: 孔子云：何陋之有  
翻译: 孔子说：有什么简陋的呢  
出处: 《陋室铭》（刘禹锡）  

123.2 云: 句末语气词，无实义  
例句: 盖大苏泛赤壁云  
翻译: （刻的）是苏轼泛舟游赤壁的情景  
出处: 《核舟记》（魏学洢）  

123.3 云: 云彩  
例句: 高峰入云，清流见底  
翻译: 山峰高耸入云，溪流清澈见底  
出处: 《答谢中书书》（陶弘景）  

123.4 云: 像云一样的  
例句: 当窗理云鬓  
翻译: 对着窗户梳理如云般的鬓发  
出处: 《木兰诗》（北朝民歌）  

123.5 云: 如此，这样  
例句: 子曰“汝安知鱼乐”云者  
翻译: 像“你怎么知道鱼的快乐”这样的话  
出处: 《庄子与惠子游于濠梁》（庄子）  

124.1 章: 印章  
例句: 又用篆章一  
翻译: 又刻了一枚篆字印章  
出处: 《核舟记》（魏学洢）  

125.1 知: 知道  
例句: 乃不知有汉，无论魏晋  
翻译: 竟然不知道有汉朝，更不用说魏晋了  
出处: 《桃花源记》（陶渊明）  

125.2 知: 懂得  
例句: 汝亦知射乎  
翻译: 你也懂得射箭吗  
出处: 《卖油翁》（欧阳修）  

125.3 知: 同“智”，智慧
例句: 两小儿笑曰：“孰为汝多知乎！”  
翻译: 两个小孩笑着说：“谁说你知识渊博呢！”  
出处: 《两小儿辩日》（列子）  

126.1 止: 停止  
例句: 一狼得骨止，一狼仍从  
翻译: 一只狼得到骨头停下来，另一只狼仍然跟着  
出处: 《狼》（蒲松龄）  

126.2 止: 制止，阻止  
例句: 河曲智叟笑而止之曰  
翻译: 河曲的智叟笑着阻止他说  
出处: 《愚公移山》（列子）  

126.3 止: 仅，只是  
例句: 担中肉尽，止有剩骨  
翻译: 担子里的肉已经卖完，只剩下骨头  
出处: 《狼》（蒲松龄）  

127.1 志: 标记，记号  
例句: 寻向所志，遂迷，不复得路  
翻译: 寻找之前做的标记，却迷失了方向，再也找不到路  
出处: 《桃花源记》（陶渊明）  

127.2 志: 做标记，做记号  
例句: 便扶向路，处处志之  
翻译: 就顺着原路回去，处处做标记  
出处: 《桃花源记》（陶渊明）  

127.3 志: 记载  
例句: 《齐谐》者，志怪者也  
翻译: 《齐谐》是记载怪异事情的书  
出处: 《逍遥游》（庄子）  

127.4 志: 心志，情志，志向  
例句: 非淡泊无以明志  
翻译: 不淡泊名利就无法明确志向  
出处: 《诫子书》（诸葛亮）  

127.5 志: 志气，意志  
例句: 必先苦其心志，劳其筋骨  
翻译: 一定要先使他的意志受折磨，筋骨受劳累  
出处: 《生于忧患，死于安乐》（孟子）  

128.1 致: 取得，得到  
例句: 家贫，无以致书以观  
翻译: 家里贫穷，没有办法得到书来看  
出处: 《送东阳马生序》（宋濂）  

128.2 致: 达到，实现  
例句: 非宁静无以致远  
翻译: 不宁静就无法达到远大的目标  
出处: 《诫子书》（诸葛亮）  

129.1 质: 询问  
例句: 余立侍左右，援疑质理  
翻译: 我站着陪侍在老师身边，提出疑问，询问道理  
出处: 《送东阳马生序》（宋濂）  

129.2 质: 资质  
例句: 非天质之卑  
翻译: 不是天资低下  
出处: 《送东阳马生序》（宋濂）  

130.1 专: 独有，独占，专享  
例句: 衣食所安，弗敢专也，必以分人  
翻译: 衣食这类用来安身的东西，不敢独自享受，一定把它分给别人  
出处: 《曹刿论战》（左传）  

130.2 专: 专门，专一  
例句: 则心不若余之专耳，岂他人之过哉  
翻译: 只是心不如我专一罢了，难道是别人的过错吗  
出处: 《送东阳马生序》（宋濂）  

131.1 走: 跑，逃跑  
例句: 双兔傍地走  
翻译: 两只兔子贴着地面跑  
出处: 《木兰诗》（北朝民歌）  

132.1 足: 脚  
例句: 细若蚊足，勾画了了  
翻译: 细得像蚊子的脚，但笔画清清楚楚  
出处: 《核舟记》（魏学洢）  

132.2 足: 足够，充足  
例句: 是故学然后知不足  
翻译: 因此学习之后才知道自己的不足  
出处: 《礼记·学记》  

132.3 足: 值得，够得上  
例句: 不足为外人道也  
翻译: 不值得对外人说  
出处: 《桃花源记》（陶渊明）  

133.1 卒: 最后，终于  
例句: 故余虽愚，卒获有所闻  
翻译: 所以我虽然愚笨，最终还是有所收获  
出处: 《送东阳马生序》（宋濂）  

134.1 作: 振作，奋发  
例句: 一鼓作气，再而衰，三而竭  
翻译: 第一次击鼓能振作士气，第二次就减弱了，第三次就耗尽了  
出处: 《曹刿论战》（左传）  

134.2 作: 发出，出现  
例句: 泉水激石，泠泠作响  
翻译: 泉水冲击着石头，发出泠泠的声响  
出处: 《与朱元思书》（吴均）  

134.3 作: 写作，创作  
例句: 属予作文以记之  
翻译: 嘱托我写一篇文章来记述这件事  
出处: 《岳阳楼记》（范仲淹）  

134.4 作: 建造  
例句: 作亭者谁  
翻译: 建造亭子的人是谁  
出处: 《醉翁亭记》（欧阳修）  

134.5 作: 做，制作  
例句: 便要还家，设酒杀鸡作食  
翻译: 就邀请他回家，摆酒杀鸡做饭  
出处: 《桃花源记》（陶渊明）  

134.6 作: 劳作，劳动  
例句: 其中往来种作，男女衣着，悉如外人  
翻译: 人们在田间来来往往耕种劳作，男女的穿戴都和外面的人一样  
出处: 《桃花源记》（陶渊明）  

134.7 作: 兴起  
例句: 盗窃乱贼而不作  
翻译: 盗窃、造反和害人的事情不发生  
出处: 《大道之行也》（礼记）  

135.1 坐: 坐着，坐下（古人双膝着地，臀部压在脚后跟上）  
例句: 船头坐三人  
翻译: 船头坐着三个人  
出处: 《核舟记》（魏学洢）  

136.1 卑鄙: 社会地位低微，见识短浅  
例句: 先帝不以臣卑鄙  
翻译: 先帝不因为我社会地位低微、见识短浅  
出处: 《出师表》（诸葛亮）  

137.1 布衣: 平民百姓  
例句: 臣本布衣，躬耕于南阳  
翻译: 我本来是平民百姓，在南阳务农  
出处: 《出师表》（诸葛亮）  

138.1 菲薄: 轻视，小看  
例句: 不宜妄自菲薄，引喻失义  
翻译: 不应该随便看轻自己，说话不恰当  
出处: 《出师表》（诸葛亮）  

139.1 其实: 其：它的，实：果实  
例句: 叶徒相似，其实味不同  
翻译: 只是叶子相似，它们的果实味道不同  
出处: 《晏子使楚》（晏子春秋）  

140.1 亲戚: 内外亲属，包括父系和母系亲属  
例句: 寡助之至，亲戚畔之  
翻译: 帮助的人少到极点，连亲属都会背叛他  
出处: 《得道多助，失道寡助》（孟子）  

141.1 驱驰: 奔走效劳  
例句: 由是感激，遂许先帝以驱驰  
翻译: 因此感动奋发，就答应为先帝奔走效劳  
出处: 《出师表》（诸葛亮）  

141.2 驱驰: 策马快走  
例句: 将军约，军中不得驱驰  
翻译: 将军规定，军营中不准策马快跑  
出处: 《周亚夫军细柳》（史记）  

142.1 无论: 不要说，更不必说  
例句: 乃不知有汉，无论魏晋  
翻译: 竟然不知道有汉朝，更不用说魏晋了  
出处: 《桃花源记》（陶渊明）  

143.1 牺牲: 指祭祀用的纯色牲畜  
例句: 牺牲玉帛，弗敢专也，必以信  
翻译: 祭祀用的牲畜、玉器和丝织品，不敢独自享用，一定如实禀告  
出处: 《曹刿论战》（左传）  

144.1 鸿儒: 博学的人  
例句: 谈笑有鸿儒，往来无白丁  
翻译: 谈笑的是博学的人，往来的没有无功名的人  
出处: 《陋室铭》（刘禹锡）  

145.1 白丁: 平民，指没有功名的人  
例句: 谈笑有鸿儒，往来无白丁  
翻译: 谈笑的是博学的人，往来的没有无功名的人  
出处: 《陋室铭》（刘禹锡）  

146.1 阡陌: 田间小路  
例句: 阡陌交通，鸡犬相闻  
翻译: 田间小路交错相通，鸡鸣狗叫的声音可以互相听到  
出处: 《桃花源记》（陶渊明）  

147.1 交通: 交错相通  
例句: 阡陌交通，鸡犬相闻  
翻译: 田间小路交错相通，鸡鸣狗叫的声音可以互相听到  
出处: 《桃花源记》（陶渊明）  

148.1 问津: 询问渡口，探求，访求  
例句: 后遂无问津者  
翻译: 后来就没有探访的人了  
出处: 《桃花源记》（陶渊明）  

149.1 绝境: 与人世隔绝的地方  
例句: 率妻子邑人来此绝境  
翻译: 带领妻子儿女和乡邻来到这个与人世隔绝的地方  
出处: 《桃花源记》（陶渊明）  

150.1 妻子: 妻子儿女  
例句: 率妻子邑人来此绝境  
翻译: 带领妻子儿女和乡邻来到这个与人世隔绝的地方  
出处: 《桃花源记》（陶渊明）
--- END OF FILE ---
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // --- 艾宾浩斯学习计划相关变量 ---
            let draggableNumbersState = {};
            const planTableStorageKey = 'shiCiEbbinghausStudyPlan2025_v1';
            let historyStack = [];
            const maxHistorySize = 20;
            let currentPage = 'ebbinghaus'; // 'ebbinghaus' 或 'practice'

            // 实词分组定义 (每组约25个实词)
            const shiCiGroups = [
                { number: 1, range: '1.1-5.99', description: '第1组: 比、鄙、兵、策、乘' },
                { number: 2, range: '6.1-10.99', description: '第2组: 持、从、达、当、道' },
                { number: 3, range: '11.1-15.99', description: '第3组: 得、度、端、恶、发' },
                { number: 4, range: '16.1-20.99', description: '第4组: 方、复、负、盖、故' },
                { number: 5, range: '21.1-25.99', description: '第5组: 顾、固、归、国、过' },
                { number: 6, range: '26.1-30.99', description: '第6组: 何、恨、胡、患、或' },
                { number: 7, range: '31.1-35.99', description: '第7组: 疾、及、即、既、加' },
                { number: 8, range: '36.1-40.99', description: '第8组: 间、见、解、就、举' },
                { number: 9, range: '41.1-45.99', description: '第9组: 具、俱、聚、遽、决' },
                { number: 10, range: '46.1-50.99', description: '第10组: 绝、类、怜、临、名' },
                { number: 11, range: '51.1-55.99', description: '第11组: 莫、乃、判、期、奇' },
                { number: 12, range: '56.1-60.99', description: '第12组: 迁、请、穷、求、去' },
                { number: 13, range: '61.1-65.99', description: '第13组: 劝、却、如、若、善' },
                { number: 14, range: '66.1-70.99', description: '第14组: 少、涉、胜、识、使' },
                { number: 15, range: '71.1-75.99', description: '第15组: 是、适、书、孰、属' },
                { number: 16, range: '76.1-80.99', description: '第16组: 数、遂、所、汤、通' },
                { number: 17, range: '81.1-85.99', description: '第17组: 图、徒、推、亡、王' },
                { number: 18, range: '86.1-90.99', description: '第18组: 望、谓、文、闻、下' },
                { number: 19, range: '91.1-95.99', description: '第19组: 鲜、相、谢、信、行' },
                { number: 20, range: '96.1-100.99', description: '第20组: 许、寻、雅、厌、要' },
                { number: 21, range: '101.1-105.99', description: '第21组: 宜、遗、贻、夷、以' },
                { number: 22, range: '106.1-110.99', description: '第22组: 义、异、易、阴、引' },
                { number: 23, range: '111.1-115.99', description: '第23组: 右、再、造、知、止' },
                { number: 24, range: '116.1-120.99', description: '第24组: 致、质、诸、主、注' },
                { number: 25, range: '121.1-125.99', description: '第25组: 资、子、自、足、卒' },
                { number: 26, range: '126.1-130.99', description: '第26组: 作、坐、座、牺牲、鸿儒' },
                { number: 27, range: '131.1-135.99', description: '第27组: 白丁、阡陌、交通、问津、绝境' },
                { number: 28, range: '136.1-140.99', description: '第28组: 妻子、无论、不必、何必、曾经' },
                { number: 29, range: '141.1-145.99', description: '第29组: 可以、于是、然后、居然、竟然' },
                { number: 30, range: '146.1-150.99', description: '第30组: 扩展词汇及复合词' }
            ];

            // 初始计划表结构 - 每组10行，共3组30行
            let initialPlanTableStructure = [];
            for (let i = 0; i < 30; i++) {
                initialPlanTableStructure.push({ seq: i + 1, date: "" });
            }

            // 折叠状态管理
            let collapseStates = {}; // 存储每个组的折叠状态
            const rowsPerGroup = 10; // 每组10行

            // --- Get references to elements ---
            const rawDataElement = document.getElementById('rawData');
            // Ebbinghaus Page Elements
            const ebbinghausHomepage = document.getElementById('ebbinghaus-homepage');
            const practicePage = document.getElementById('practice-page');
            const gotoPracticeButton = document.getElementById('goto-practice-button');
            const backToPlanButton = document.getElementById('back-to-plan-button');
            const backToHomeButton = document.getElementById('back-to-home-button');
            const statusBackToHomeButton = document.getElementById('status-back-to-home-button');

            // Selection Screen Elements
            const selectionContainer = document.getElementById('practice-selection-container');
            const startNumberInput = document.getElementById('start-number');
            const endNumberInput = document.getElementById('end-number');
            const startButton = document.getElementById('start-button');
            const selectionError = document.getElementById('selection-error');
            // Flashcard Screen Elements
            const flashcardContainer = document.getElementById('flashcard-container');
            const questionArea = document.getElementById('question-area');
            const inputArea = document.getElementById('input-area');
            const resultDisplayArea = document.getElementById('result-display-area');
            const feedbackMessage = document.getElementById('feedback-message');
            const meaningDisplay = document.getElementById('meaning-display');
            const translationDisplay = document.getElementById('translation-display');
            const sourceDisplay = document.getElementById('source-display');
            const nextQuestionButton = document.getElementById('next-question-button');
            // Other Elements
            const exportButton = document.getElementById('export-button');
            const statusBar = document.getElementById('status-bar');
            const totalQuestionsSpan = document.getElementById('total-questions');
            const remainingQuestionsSpan = document.getElementById('remaining-questions');

            // --- Global Variables ---
            let allFlashcards = []; // Holds all parsed cards
            let currentDeck = [];   // Holds the deck for the current practice session (filtered or full)
            let correctFirstTry = new Set();
            let incorrectAttempts = {};
            let currentCardIndex = 0;
            let totalInitialCards = 0; // Total cards in the *current* practice session

            // --- Utility Functions ---

            function parseNumbering(numStr) {
                if (!numStr || typeof numStr !== 'string') return null;
                const parts = numStr.trim().split('.');
                const major = parseInt(parts[0], 10);
                // Handle "X" as "X.0" and ensure minor is treated as 0 if missing or invalid
                const minor = parts.length > 1 ? parseInt(parts[1], 10) : 0;

                if (isNaN(major)) return null; // Major part must be a number
                return {
                    major: major,
                    minor: isNaN(minor) ? 0 : minor // Default minor to 0 if not parseable
                };
            }

            // Compares two numbering objects {major, minor}
            // Returns -1 if num1 < num2, 0 if equal, 1 if num1 > num2
            function compareNumbering(num1, num2) {
                if (!num1 || !num2) return 0; // Treat nulls as equal or handle differently if needed
                if (num1.major < num2.major) return -1;
                if (num1.major > num2.major) return 1;
                // Major parts are equal, compare minor parts
                if (num1.minor < num2.minor) return -1;
                if (num1.minor > num2.minor) return 1;
                return 0; // Both major and minor are equal
            }


             function parseRawData(text) {
                 const lines = text.trim().split('\n');
                 const flashcards = [];
                 let counter = 0;
                 let potentialEntry = {};
                 let linesForCurrentEntry = 0;

                 for (let i = 0; i < lines.length; i++) {
                     const line = lines[i].trim();
                     if (line.startsWith('---') || line === '') {
                         potentialEntry = {};
                         linesForCurrentEntry = 0;
                         continue;
                     }

                     const meaningMatch = line.match(/^(\d+\.\d+)?\s*([^:]+):\s*(.+)/);

                     if (meaningMatch && linesForCurrentEntry === 0) {
                         potentialEntry = {
                             originalNumbering: meaningMatch[1] ? meaningMatch[1].trim() : null,
                             word: meaningMatch[2].trim(),
                             meaning: meaningMatch[3].trim(),
                             originalIndex: -1 // Placeholder
                         };
                         linesForCurrentEntry = 1;
                     } else if (linesForCurrentEntry === 1 && line.startsWith('例句:')) {
                         potentialEntry.example = line.substring('例句:'.length).trim();
                         linesForCurrentEntry = 2;
                     } else if (linesForCurrentEntry === 2 && line.startsWith('翻译:')) {
                         potentialEntry.translation = line.substring('翻译:'.length).trim();
                         linesForCurrentEntry = 3;
                     } else if (linesForCurrentEntry === 3 && line.startsWith('出处:')) {
                         potentialEntry.source = line.substring('出处:'.length).trim();
                         linesForCurrentEntry = 4;

                         if (potentialEntry.word && potentialEntry.meaning && potentialEntry.example && potentialEntry.translation && potentialEntry.source) {
                             potentialEntry.originalIndex = counter++;
                             // Attempt to parse numbering here for filtering later
                             potentialEntry.numberingObj = parseNumbering(potentialEntry.originalNumbering);
                             if (!potentialEntry.numberingObj) {
                                console.warn(`Card with word "${potentialEntry.word}" has invalid or missing numbering "${potentialEntry.originalNumbering}". It might be excluded from range filtering.`);
                             }
                             flashcards.push(potentialEntry);
                         } else {
                             console.warn(`Data Integrity Issue: Incomplete data for potential entry starting with "${potentialEntry.word}". Skipping.`);
                         }
                         potentialEntry = {};
                         linesForCurrentEntry = 0;

                     } else {
                         if (linesForCurrentEntry > 0) {
                             console.warn(`Formatting Issue: Unexpected line "${line}" encountered while parsing entry starting with "${potentialEntry.word}". Skipping incomplete entry.`);
                         } else if (line) {
                             console.warn(`Formatting Issue: Line "${line}" does not start a valid entry. Skipping line.`);
                         }
                         potentialEntry = {};
                         linesForCurrentEntry = 0;
                     }
                 }

                 console.log("Total Parsed Flashcards:", flashcards.length);
                 if (flashcards.length !== counter) {
                     console.error(`Internal Counter Mismatch! Parsed: ${flashcards.length}, Counter: ${counter}`);
                 }
                 return flashcards;
             }


            function shuffleArray(array) {
                for (let i = array.length - 1; i > 0; i--) {
                    const j = Math.floor(Math.random() * (i + 1));
                    [array[i], array[j]] = [array[j], array[i]];
                }
            }

            function updateStatus() {
                totalQuestionsSpan.textContent = totalInitialCards; // Total for this session
                remainingQuestionsSpan.textContent = currentDeck.length; // Remaining in this session
            }

            function normalizeAnswer(answer) {
                 // 移除所有标点符号、空格和特殊字符，只保留中文字符、英文字母和数字
                 return answer.trim().replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '');
             }

            // --- Practice Session Logic ---

            function initializePractice(deck) {
                if (!deck || deck.length === 0) {
                    selectionError.textContent = "没有找到符合条件的词条。请检查输入范围。";
                    // Keep selection screen visible
                     selectionContainer.classList.remove('hidden');
                     flashcardContainer.classList.add('hidden');
                     statusBar.classList.add('hidden');
                     exportButton.classList.add('hidden');
                    return;
                }

                currentDeck = deck;
                totalInitialCards = currentDeck.length;
                currentCardIndex = 0;
                correctFirstTry = new Set();
                incorrectAttempts = {};

                shuffleArray(currentDeck);
                console.log(`Starting practice with ${totalInitialCards} cards.`);

                // Hide selection, show flashcards
                selectionContainer.classList.add('hidden');
                flashcardContainer.classList.remove('hidden');
                statusBar.classList.remove('hidden');
                exportButton.classList.remove('hidden');

                displayQuestion(); // Display the first question of the selected deck
            }

            function displayQuestion() {
                resultDisplayArea.style.display = 'none'; // Hide previous result

                if (currentDeck.length === 0) {
                    questionArea.innerHTML = `<div style='text-align: center; font-size: 1.5em; color: #3a8e3a;'>🎉 恭喜！您已完成本次所选范围的练习！ (${totalInitialCards} 个已完成) 🎉</div>`;
                    inputArea.innerHTML = '';
                     // Add a button to restart or go back to selection? Optional.
                     // For now, just show completion.
                    updateStatus();
                    return;
                }

                // Handle index wrapping
                if (currentCardIndex >= currentDeck.length) {
                    currentCardIndex = 0;
                     if (currentDeck.length === 0) { // Double check needed if last item was removed
                        displayQuestion(); // Will show completion message
                        return;
                     }
                }


                const cardData = currentDeck[currentCardIndex];
                if (!cardData) {
                    console.error("Error: No card data at index", currentCardIndex, "Deck size:", currentDeck.length);
                    questionArea.innerHTML = "错误：无法加载下一个问题。";
                    inputArea.innerHTML = '';
                    // Attempt recovery or stop? For now, just stop.
                    return;
                }

                // Display numbering and question
                const numberingPrefix = cardData.originalNumbering ? `<span class="numbering">${cardData.originalNumbering}</span>` : '';
                questionArea.innerHTML = `${numberingPrefix}${cardData.example} （<strong style="color: #c00;">${cardData.word}</strong>）`;

                // Setup input
                const inputHTML = `
                    <input type="text" class="answer-input" id="answer-input-0" placeholder="请输入实词 “${cardData.word}” 的含义" autocomplete="off">
                    <p class="instruction">输入含义后按回车键确认</p>
                `;
                inputArea.innerHTML = inputHTML;

                const inputField = document.getElementById('answer-input-0');
                if (inputField) {
                    inputField.value = '';
                    inputField.disabled = false;
                    inputField.focus();
                    inputField.removeEventListener('keypress', handleKeyPress); // Remove old before adding new
                    inputField.addEventListener('keypress', handleKeyPress);
                }

                updateStatus();
            }

             function handleKeyPress(event) {
                if (event.key === 'Enter') {
                    event.preventDefault();
                    checkAnswer();
                }
            }

            function checkAnswer() {
                if (currentDeck.length === 0 || currentCardIndex >= currentDeck.length) return;
                const cardData = currentDeck[currentCardIndex];
                if (!cardData) return;

                const inputElement = document.getElementById('answer-input-0');
                const userAnswerRaw = inputElement ? inputElement.value : '';
                const normalizedUserAnswer = normalizeAnswer(userAnswerRaw);
                const correctAnswers = cardData.meaning.split(/[/；;，、]/).map(ans => normalizeAnswer(ans.trim()));
                const normalizedCorrectAnswerString = cardData.meaning;

                // 也检查完整答案（移除分隔符后）
                const fullAnswer = normalizeAnswer(cardData.meaning);

                if (inputElement) {
                    inputElement.disabled = true;
                    inputElement.removeEventListener('keypress', handleKeyPress);
                }

                // 检查是否匹配任何单个答案或完整答案
                const isCorrect = correctAnswers.some(correctAnswer => normalizedUserAnswer === correctAnswer) ||
                                normalizedUserAnswer === fullAnswer;
                const originalIndex = cardData.originalIndex; // Use for consistent tracking

                // 详细调试信息
                console.log(`=== 答案验证调试信息 ===`);
                console.log(`词条: ${cardData.originalNumbering || '#' + (originalIndex + 1)} (${cardData.word})`);
                console.log(`原始含义: "${cardData.meaning}"`);
                console.log(`用户输入: "${userAnswerRaw}"`);
                console.log(`标准化用户输入: "${normalizedUserAnswer}"`);
                console.log(`完整答案(标准化): "${fullAnswer}"`);
                console.log(`分割后的正确答案:`, correctAnswers);
                correctAnswers.forEach((ans, index) => {
                    console.log(`  答案${index + 1}: "${ans}" (长度: ${ans.length})`);
                    console.log(`  匹配检查: "${normalizedUserAnswer}" === "${ans}" ? ${normalizedUserAnswer === ans}`);
                });
                console.log(`完整答案匹配: "${normalizedUserAnswer}" === "${fullAnswer}" ? ${normalizedUserAnswer === fullAnswer}`);
                console.log(`最终匹配结果: ${isCorrect}`);
                console.log(`========================`);

                // Populate result area
                meaningDisplay.textContent = cardData.meaning;
                translationDisplay.textContent = cardData.translation;
                sourceDisplay.textContent = cardData.source;

                if (isCorrect) {
                    feedbackMessage.textContent = "正确!";
                    feedbackMessage.className = "correct";
                    if (!incorrectAttempts.hasOwnProperty(originalIndex)) {
                        correctFirstTry.add(originalIndex);
                    }
                    currentDeck.splice(currentCardIndex, 1); // Remove from current practice deck
                    // Don't increment index after splice, unless it was the last item
                    if (currentCardIndex >= currentDeck.length && currentDeck.length > 0) {
                        currentCardIndex = 0;
                    }
                    updateStatus();
                } else {
                    feedbackMessage.textContent = "错误!";
                    feedbackMessage.className = "incorrect";
                    incorrectAttempts[originalIndex] = (incorrectAttempts[originalIndex] || 0) + 1;
                    currentCardIndex++; // Move to next card in the deck
                    if (currentCardIndex >= currentDeck.length) {
                        currentCardIndex = 0; // Wrap index
                    }
                    // Don't update status as deck size is unchanged
                }

                resultDisplayArea.style.display = 'block'; // Show feedback
                nextQuestionButton.focus();
            }

            // --- Event Listeners ---

            startButton.addEventListener('click', () => {
                const startNumStr = startNumberInput.value.trim();
                const endNumStr = endNumberInput.value.trim();
                selectionError.textContent = ''; // Clear previous errors

                // Use null to represent unbounded range if input is empty
                const startNumObj = startNumStr ? parseNumbering(startNumStr) : null;
                const endNumObj = endNumStr ? parseNumbering(endNumStr) : null;

                // --- Input Validation ---
                if (startNumStr && !startNumObj) {
                    selectionError.textContent = '起始编号格式无效 (应为 X.Y 或 X)';
                    return;
                }
                if (endNumStr && !endNumObj) {
                    selectionError.textContent = '结束编号格式无效 (应为 X.Y 或 X)';
                    return;
                }
                if (startNumObj && endNumObj && compareNumbering(startNumObj, endNumObj) > 0) {
                    selectionError.textContent = '起始编号不能大于结束编号';
                    return;
                }

                // --- Filtering Logic ---
                const filteredDeck = allFlashcards.filter(card => {
                    const cardNumObj = card.numberingObj; // Use pre-parsed object
                    if (!cardNumObj) return false; // Exclude cards with invalid numbering

                    const afterStart = startNumObj ? compareNumbering(cardNumObj, startNumObj) >= 0 : true;
                    const beforeEnd = endNumObj ? compareNumbering(cardNumObj, endNumObj) <= 0 : true;

                    return afterStart && beforeEnd;
                });

                // --- Start the practice session ---
                initializePractice(filteredDeck);

            });

            nextQuestionButton.addEventListener('click', displayQuestion);

            exportButton.addEventListener('click', () => {
                let exportText = `${new Date().toLocaleString()} 实词默写记录\n`;
                // Note: Export now reflects the *subset* practiced in this session
                exportText += `练习范围: ${startNumberInput.value.trim() || '开始'} - ${endNumberInput.value.trim() || '结尾'}\n`;
                exportText += `本次练习总题数: ${totalInitialCards}\n\n`;

                 exportText += "--- 一次默写正确的实词 ---\n";
                 let firstTryList = [];
                 // Filter based on the *original* full list but check against current session results
                 allFlashcards.forEach(card => {
                    // Check if card was *part of this session* (i.e., in the initial filtered deck)
                    // This requires knowing the initial deck, which we overwrote. Let's track differently.
                    // We can check if its originalIndex is in either correctFirstTry or incorrectAttempts
                    // AND it's not currently left in currentDeck.
                    const inThisSession = correctFirstTry.has(card.originalIndex) || incorrectAttempts.hasOwnProperty(card.originalIndex);
                    const answeredCorrectlyEventually = !currentDeck.some(c => c.originalIndex === card.originalIndex);

                    if (inThisSession && answeredCorrectlyEventually && correctFirstTry.has(card.originalIndex) && !incorrectAttempts.hasOwnProperty(card.originalIndex)) {
                         firstTryList.push(card);
                     }
                 });
                 if (firstTryList.length > 0) {
                    firstTryList.sort((a,b) => compareNumbering(a.numberingObj, b.numberingObj)).forEach(card => {
                         const numPrefix = card.originalNumbering || `#${card.originalIndex + 1}`;
                         exportText += `${numPrefix}. ${card.word} (${card.meaning})   例句: ${card.example}\n`;
                     });
                 } else {
                     exportText += "(无)\n";
                 }


                 exportText += "\n--- 多次默写正确的实词 ---\n";
                 let multiTryList = [];
                  allFlashcards.forEach(card => {
                      const originalIndex = card.originalIndex;
                      const inThisSession = correctFirstTry.has(originalIndex) || incorrectAttempts.hasOwnProperty(originalIndex);
                      const answeredCorrectlyEventually = !currentDeck.some(c => c.originalIndex === originalIndex);

                      if (inThisSession && answeredCorrectlyEventually && incorrectAttempts.hasOwnProperty(originalIndex)) {
                           multiTryList.push({ ...card, attempts: incorrectAttempts[originalIndex] + 1 });
                      }
                  });

                 if (multiTryList.length > 0) {
                     multiTryList.sort((a,b) => compareNumbering(a.numberingObj, b.numberingObj)).forEach(card => {
                         const numPrefix = card.originalNumbering || `#${card.originalIndex + 1}`;
                         exportText += `${numPrefix}. ${card.word} (${card.meaning})   例句: ${card.example}   (尝试 ${card.attempts} 次)\n`;
                     });
                 } else {
                     exportText += "(无)\n";
                 }

                 exportText += "\n--- 尚未掌握的实词 (本次练习范围) ---\n";
                 if (currentDeck.length > 0) { // These are the ones remaining *from this session*
                     currentDeck.sort((a,b) => compareNumbering(a.numberingObj, b.numberingObj)).forEach(card => {
                          const attempts = incorrectAttempts[card.originalIndex] || 0;
                          const numPrefix = card.originalNumbering || `#${card.originalIndex + 1}`;
                          exportText += `${numPrefix}. ${card.word} (${card.meaning})   例句: ${card.example}   (已尝试 ${attempts} 次)\n`;
                     });
                 } else {
                     exportText += "(全部完成)\n";
                 }


                 const blob = new Blob([exportText], { type: 'text/plain;charset=utf-8' });
                 const link = document.createElement('a');
                 const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-').replace('T', '_');
                 link.href = URL.createObjectURL(blob);
                 link.download = `文言文实词默写记录_${timestamp}.txt`;
                 document.body.appendChild(link);
                 link.click();
                 document.body.removeChild(link);
                 URL.revokeObjectURL(link.href);
             });


            // --- 艾宾浩斯学习计划功能 ---

            // 页面切换功能
            function showEbbinghausPage() {
                ebbinghausHomepage.style.display = 'block';
                practicePage.style.display = 'none';
                currentPage = 'ebbinghaus';
            }

            function showPracticePage() {
                ebbinghausHomepage.style.display = 'none';
                practicePage.style.display = 'block';
                currentPage = 'practice';
            }

            // 生成可拖拽的学习单元
            function generateDraggableNumbers() {
                const container = document.getElementById('draggable-numbers-container');
                if (!container) return;

                container.innerHTML = '';
                shiCiGroups.forEach(group => {
                    const numberDiv = document.createElement('div');
                    numberDiv.className = 'draggable-number';
                    numberDiv.textContent = `第${group.number}组`;
                    numberDiv.title = group.description;
                    numberDiv.draggable = true;
                    numberDiv.dataset.value = group.number;
                    numberDiv.dataset.range = group.range;

                    // 点击直接进入练习
                    numberDiv.addEventListener('click', () => {
                        startPracticeFromGroup(group.range);
                    });

                    // 拖拽事件
                    numberDiv.addEventListener('dragstart', (e) => {
                        e.dataTransfer.setData('text/plain', group.number);
                        e.dataTransfer.setData('application/json', JSON.stringify({
                            number: group.number,
                            range: group.range,
                            description: group.description
                        }));
                    });

                    container.appendChild(numberDiv);
                });
            }

            // 从学习组启动练习
            function startPracticeFromGroup(range) {
                showPracticePage();
                const [start, end] = range.split('-');
                startNumberInput.value = start;
                endNumberInput.value = end;

                // 自动开始练习
                setTimeout(() => {
                    startButton.click();
                }, 100);
            }

            // 生成学习计划表
            function generateStudyPlanTable(planDataArray = initialPlanTableStructure) {
                const tbody = document.querySelector('#study-plan-table tbody');
                if (!tbody) return;

                tbody.innerHTML = '';

                // 按组生成表格
                for (let groupIndex = 0; groupIndex < Math.ceil(planDataArray.length / rowsPerGroup); groupIndex++) {
                    const groupStart = groupIndex * rowsPerGroup;
                    const groupEnd = Math.min(groupStart + rowsPerGroup, planDataArray.length);
                    const groupNumber = groupIndex + 1;

                    // 创建组头
                    const groupHeaderTr = createGroupHeader(groupNumber, groupStart, groupEnd);
                    tbody.appendChild(groupHeaderTr);

                    // 创建组内容行
                    for (let i = groupStart; i < groupEnd; i++) {
                        const tr = createPlanRowUI(planDataArray[i], i, groupNumber);
                        tr.classList.add(`group-${groupNumber}-row`);
                        tbody.appendChild(tr);
                    }
                }

                // 生成表格后更新所有组头信息
                setTimeout(() => {
                    updateAllGroupHeaders();
                }, 100);
            }

            // 计算学习组中的实词个数
            function getGroupWordCount(groupNumber) {
                const group = shiCiGroups.find(g => g.number === groupNumber);
                if (!group) return 0;

                const [start, end] = group.range.split('-');
                const startNum = parseFloat(start);
                const endNum = parseFloat(end);

                // 计算范围内的实词个数
                let count = 0;
                allFlashcards.forEach(card => {
                    const cardNum = parseFloat(card.number);
                    if (cardNum >= startNum && cardNum <= endNum) {
                        count++;
                    }
                });

                return count;
            }

            // 创建组头
            function createGroupHeader(groupNumber, startIndex, endIndex) {
                const tr = document.createElement('tr');
                tr.className = 'group-header-row';

                // 折叠按钮
                const foldCell = document.createElement('td');
                const collapseToggle = document.createElement('div');
                collapseToggle.className = 'collapse-toggle';
                collapseToggle.textContent = '▼';
                collapseToggle.dataset.group = groupNumber;
                collapseToggle.title = '折叠/展开';
                collapseToggle.addEventListener('click', () => toggleGroup(groupNumber));
                foldCell.appendChild(collapseToggle);
                tr.appendChild(foldCell);

                // 组信息 - 显示对应的学习单元个数
                const infoCell = document.createElement('td');
                infoCell.colSpan = 10;
                infoCell.className = `group-${groupNumber}-header-info`;
                infoCell.style.textAlign = 'left';
                infoCell.style.paddingLeft = '20px';
                tr.appendChild(infoCell);

                // 初始化组头信息
                updateGroupHeaderInfo(groupNumber, startIndex, endIndex);

                return tr;
            }

            // 更新组头信息
            function updateGroupHeaderInfo(groupNumber, startIndex, endIndex) {
                const infoCell = document.querySelector(`.group-${groupNumber}-header-info`);
                if (!infoCell) return;

                // 计算这个计划组对应的学习单元个数
                let totalUnits = 0;
                const groupRows = document.querySelectorAll(`.group-${groupNumber}-row`);
                groupRows.forEach(row => {
                    const contentCell = row.querySelector('.study-content-cell');
                    if (contentCell) {
                        const tags = contentCell.querySelectorAll('.dropped-item-tag');
                        totalUnits += tags.length;
                    }
                });

                infoCell.textContent = `第${groupNumber}组 (第${startIndex + 1}-${endIndex}天) - ${totalUnits}个学习单元`;
            }

            // 更新所有组头信息
            function updateAllGroupHeaders() {
                for (let groupIndex = 0; groupIndex < Math.ceil(initialPlanTableStructure.length / rowsPerGroup); groupIndex++) {
                    const groupStart = groupIndex * rowsPerGroup;
                    const groupEnd = Math.min(groupStart + rowsPerGroup, initialPlanTableStructure.length);
                    const groupNumber = groupIndex + 1;
                    updateGroupHeaderInfo(groupNumber, groupStart, groupEnd);
                }
            }

            // 切换组的折叠状态
            function toggleGroup(groupNumber) {
                const groupRows = document.querySelectorAll(`.group-${groupNumber}-row`);
                const collapseToggle = document.querySelector(`[data-group="${groupNumber}"]`);

                if (groupRows.length > 0 && collapseToggle) {
                    const isCollapsed = collapseToggle.classList.contains('collapsed');

                    if (isCollapsed) {
                        // 展开
                        groupRows.forEach(row => row.classList.remove('row-group-collapsed'));
                        collapseToggle.classList.remove('collapsed');
                        collapseToggle.textContent = '▼';
                        collapseStates[groupNumber] = false;
                    } else {
                        // 折叠
                        groupRows.forEach(row => row.classList.add('row-group-collapsed'));
                        collapseToggle.classList.add('collapsed');
                        collapseToggle.textContent = '▶';
                        collapseStates[groupNumber] = true;
                    }
                }
            }

            // 创建计划表行UI
            function createPlanRowUI(rowData, rowIndex, groupNumber) {
                const tr = document.createElement('tr');

                // 空的折叠列（组内行不需要折叠按钮）
                const emptyCell = document.createElement('td');
                emptyCell.textContent = '—';
                emptyCell.style.color = '#ccc';
                tr.appendChild(emptyCell);

                // 序号
                const seqCell = document.createElement('td');
                seqCell.textContent = rowData.seq;
                tr.appendChild(seqCell);

                // 学习日期
                const dateCell = document.createElement('td');
                const dateInput = document.createElement('input');
                dateInput.type = 'text';
                dateInput.className = 'study-date-input';
                dateInput.placeholder = '如:0729';
                dateInput.value = rowData.date || '';
                dateInput.maxLength = 6;
                dateCell.appendChild(dateInput);
                tr.appendChild(dateCell);

                // 学习内容
                const contentCell = document.createElement('td');
                contentCell.className = 'study-content-cell';
                setupDropZone(contentCell);
                tr.appendChild(contentCell);

                // 复习单元格
                const reviewDays = [1, 2, 3, 5, 8, 16, 31];
                reviewDays.forEach((day, index) => {
                    const reviewCell = document.createElement('td');
                    const container = document.createElement('div');
                    container.className = 'review-cell-content';

                    // 输入框
                    const reviewInput = document.createElement('input');
                    reviewInput.type = 'text';
                    reviewInput.placeholder = '单元号';
                    container.appendChild(reviewInput);

                    // 完成按钮
                    const completeBtn = document.createElement('button');
                    completeBtn.textContent = '完成';
                    completeBtn.addEventListener('click', () => {
                        toggleReviewComplete(reviewInput, completeBtn);
                    });
                    container.appendChild(completeBtn);

                    reviewCell.appendChild(container);
                    tr.appendChild(reviewCell);
                });

                return tr;
            }

            // 设置拖放区域
            function setupDropZone(cell) {
                cell.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    cell.classList.add('drag-over');
                });

                cell.addEventListener('dragleave', () => {
                    cell.classList.remove('drag-over');
                });

                cell.addEventListener('drop', (e) => {
                    e.preventDefault();
                    cell.classList.remove('drag-over');

                    try {
                        const groupData = JSON.parse(e.dataTransfer.getData('application/json'));
                        addDroppedItem(cell, groupData);
                    } catch (error) {
                        console.error('拖放数据解析失败:', error);
                    }
                });
            }

            // 添加拖放的项目
            function addDroppedItem(cell, groupData) {
                const tag = document.createElement('span');
                tag.className = 'dropped-item-tag';
                tag.textContent = `第${groupData.number}组`;
                tag.title = groupData.description;
                tag.dataset.value = groupData.number;
                tag.dataset.range = groupData.range;

                // 点击进入练习
                tag.addEventListener('click', () => {
                    startPracticeFromGroup(groupData.range);
                });

                // 删除按钮
                const removeBtn = document.createElement('span');
                removeBtn.className = 'remove-tag';
                removeBtn.textContent = '×';
                removeBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    tag.remove();
                    // 删除后更新组头信息
                    updateAllGroupHeaders();
                });
                tag.appendChild(removeBtn);

                cell.appendChild(tag);

                // 添加后更新组头信息
                updateAllGroupHeaders();
            }

            // 切换复习完成状态
            function toggleReviewComplete(input, button) {
                if (input.readOnly) {
                    // 当前是完成状态，切换为未完成
                    input.readOnly = false;
                    input.style.backgroundColor = '';
                    button.textContent = '完成';
                    button.disabled = false;
                } else {
                    // 当前是未完成状态，切换为完成
                    input.readOnly = true;
                    input.style.backgroundColor = '#f0f0f0';
                    button.textContent = '已完成';
                    button.disabled = true;
                }
            }

            // 保存计划
            function savePlan() {
                try {
                    const planData = {
                        tableData: [],
                        collapseStates: collapseStates,
                        timestamp: new Date().toISOString()
                    };

                    // 获取所有非组头的行
                    const dataRows = document.querySelectorAll('#study-plan-table tbody tr:not(.group-header-row)');
                    dataRows.forEach((tr, index) => {
                        const rowData = {
                            seq: parseInt(tr.cells[1].textContent),
                            date: tr.cells[2].querySelector('.study-date-input').value || '',
                            studyContent: [],
                            reviewCells: []
                        };

                        // 获取学习内容
                        tr.cells[3].querySelectorAll('.dropped-item-tag').forEach(tag => {
                            if (tag.dataset.value) {
                                rowData.studyContent.push({
                                    number: tag.dataset.value,
                                    range: tag.dataset.range
                                });
                            }
                        });

                        // 获取复习状态和输入内容
                        for (let i = 4; i < tr.cells.length; i++) {
                            const container = tr.cells[i].querySelector('.review-cell-content');
                            if (container) {
                                const inputBox = container.querySelector('input[type="text"]');
                                const button = container.querySelector('button');
                                rowData.reviewCells.push({
                                    completed: inputBox.readOnly,
                                    inputValue: inputBox.value || ''
                                });
                            }
                        }

                        planData.tableData.push(rowData);
                    });

                    localStorage.setItem(planTableStorageKey, JSON.stringify(planData));
                    alert('学习计划已保存成功！');
                } catch (error) {
                    console.error('保存计划失败:', error);
                    alert('保存计划失败：' + error.message);
                }
            }

            // 重置计划
            function resetPlan() {
                if (confirm("确定要重置学习计划表吗？此操作不可撤销。")) {
                    localStorage.removeItem(planTableStorageKey);
                    generateStudyPlanTable();
                    alert("学习计划表已重置。");
                }
            }

            // 添加新学习日
            function addPlanRow() {
                const tbody = document.querySelector('#study-plan-table tbody');
                const newSeq = tbody.children.length + 1;
                const newRow = createPlanRowUI({ seq: newSeq, date: "" }, newSeq - 1);
                tbody.appendChild(newRow);
            }

            // 删减学习日
            function removePlanRow() {
                const tbody = document.querySelector('#study-plan-table tbody');
                if (tbody.children.length > 1) {
                    tbody.removeChild(tbody.lastElementChild);
                } else {
                    alert('至少需要保留一行学习计划');
                }
            }

            // 打印计划
            function printPlan() {
                window.print();
            }

            // 事件监听器
            if (gotoPracticeButton) {
                gotoPracticeButton.addEventListener('click', showPracticePage);
            }

            if (backToPlanButton) {
                backToPlanButton.addEventListener('click', showEbbinghausPage);
            }

            if (backToHomeButton) {
                backToHomeButton.addEventListener('click', showEbbinghausPage);
            }

            if (statusBackToHomeButton) {
                statusBackToHomeButton.addEventListener('click', showEbbinghausPage);
            }

            // 计划表按钮事件
            const savePlanButton = document.getElementById('save-plan-button');
            const resetPlanButton = document.getElementById('reset-plan-button');
            const addPlanRowButton = document.getElementById('add-plan-row-button');
            const removePlanRowButton = document.getElementById('remove-plan-row-button');
            const printButton = document.getElementById('print-button');

            if (savePlanButton) {
                savePlanButton.addEventListener('click', savePlan);
            }
            if (resetPlanButton) {
                resetPlanButton.addEventListener('click', resetPlan);
            }
            if (addPlanRowButton) {
                addPlanRowButton.addEventListener('click', addPlanRow);
            }
            if (removePlanRowButton) {
                removePlanRowButton.addEventListener('click', removePlanRow);
            }
            if (printButton) {
                printButton.addEventListener('click', printPlan);
            }

            // --- Initial Page Load ---
            const rawText = rawDataElement.textContent;
            allFlashcards = parseRawData(rawText); // Parse all data initially

             if (allFlashcards.length === 0) {
                 console.error("Failed to parse any flashcards! Check data format and console warnings.");
                 selectionContainer.innerHTML = "<h1>错误</h1><p>无法加载实词数据。请检查数据格式及浏览器控制台错误信息。</p>";
                 // Hide everything else
                 flashcardContainer.classList.add('hidden');
                 statusBar.classList.add('hidden');
                 exportButton.classList.add('hidden');
             } else {
                console.log(`Successfully parsed ${allFlashcards.length} flashcards.`);

                // 初始化艾宾浩斯学习计划页面
                generateDraggableNumbers();
                generateStudyPlanTable();

                // 默认显示艾宾浩斯页面
                showEbbinghausPage();
             }
        });
    </script>

</body>
</html>